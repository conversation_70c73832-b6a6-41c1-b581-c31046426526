#!/usr/bin/env python3
"""
Enhanced LipNet architecture with BiLSTM + Attention for achieving >80% validation accuracy.
This will be used if the simplified model plateaus below 80%.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import pandas as pd
import numpy as np
from pathlib import Path
import json
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

class AugmentedDataset(Dataset):
    """Dataset for augmented training data with optional TTA"""
    
    def __init__(self, manifest_path, augment=False, tta=False):
        self.manifest = pd.read_csv(manifest_path)
        self.augment = augment
        self.tta = tta  # Test Time Augmentation
        
        # Verify all files exist
        valid_rows = []
        for idx, row in self.manifest.iterrows():
            if Path(row['video_path']).exists():
                valid_rows.append(row)
        
        self.manifest = pd.DataFrame(valid_rows).reset_index(drop=True)
        
        # Print dataset info
        print(f"📊 Dataset loaded: {len(self.manifest)} videos")
        class_counts = self.manifest['word'].value_counts()
        print(f"   Class distribution: {dict(class_counts)}")
        if tta:
            print(f"   🔄 Test Time Augmentation enabled")
    
    def __len__(self):
        return len(self.manifest)
    
    def apply_light_augmentation(self, video_tensor):
        """Apply light augmentation for TTA"""
        augmented = video_tensor.clone()
        
        # Light brightness adjustment
        if torch.rand(1) < 0.5:
            brightness = (torch.rand(1) - 0.5) * 0.2  # ±10% brightness
            augmented = augmented + brightness
        
        # Light horizontal flip
        if torch.rand(1) < 0.5:
            augmented = torch.flip(augmented, dims=[-1])
        
        # Small amount of noise
        if torch.rand(1) < 0.3:
            noise = torch.randn_like(augmented) * 0.01
            augmented = augmented + noise
        
        return torch.clamp(augmented, -3, 3)
    
    def __getitem__(self, idx):
        row = self.manifest.iloc[idx]
        
        # Load preprocessed tensor
        video_tensor = torch.load(row['video_path'])
        
        # Ensure correct shape: (1, T, H, W)
        if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
            pass  # Already correct
        elif video_tensor.dim() == 3:
            video_tensor = video_tensor.unsqueeze(0)
        else:
            raise ValueError(f"Unexpected tensor shape: {video_tensor.shape}")
        
        # Apply TTA if enabled
        if self.tta:
            video_tensor = self.apply_light_augmentation(video_tensor)
        
        label = torch.tensor(row['label'], dtype=torch.long)
        
        return video_tensor, label

class EnhancedLipNet(nn.Module):
    """Enhanced LipNet with 3D CNN + BiLSTM + Attention for >80% accuracy"""
    
    def __init__(self, num_classes=5, dropout=0.4):
        super(EnhancedLipNet, self).__init__()
        
        # Enhanced 3D CNN backbone with more capacity
        self.conv3d_1 = nn.Conv3d(1, 64, kernel_size=(3, 7, 7), stride=(1, 2, 2), padding=(1, 3, 3))
        self.bn3d_1 = nn.BatchNorm3d(64)
        self.pool3d_1 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        
        self.conv3d_2 = nn.Conv3d(64, 128, kernel_size=(3, 5, 5), stride=(1, 1, 1), padding=(1, 2, 2))
        self.bn3d_2 = nn.BatchNorm3d(128)
        self.pool3d_2 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        
        self.conv3d_3 = nn.Conv3d(128, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))
        self.bn3d_3 = nn.BatchNorm3d(256)
        self.pool3d_3 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        
        # Additional conv layer for more capacity
        self.conv3d_4 = nn.Conv3d(256, 512, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))
        self.bn3d_4 = nn.BatchNorm3d(512)
        
        # Calculate LSTM input size: 512 * 7 * 7 = 25088
        self.lstm_input_size = 512 * 7 * 7
        
        # BiLSTM for temporal modeling
        self.lstm = nn.LSTM(
            input_size=self.lstm_input_size,
            hidden_size=512,
            num_layers=2,
            batch_first=True,
            dropout=dropout,
            bidirectional=True
        )
        
        # Multi-head attention
        self.attention = nn.MultiheadAttention(
            embed_dim=1024,  # 512 * 2 (bidirectional)
            num_heads=16,
            dropout=dropout,
            batch_first=True
        )
        
        # Enhanced classification head
        self.classifier = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(1024, 512),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(512),
            nn.Dropout(dropout),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(256),
            nn.Dropout(dropout),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            nn.Linear(128, num_classes)
        )
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize model weights"""
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm3d) or isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LSTM):
                for name, param in m.named_parameters():
                    if 'weight' in name:
                        nn.init.orthogonal_(param)
                    elif 'bias' in name:
                        nn.init.constant_(param, 0)
    
    def forward(self, x):
        # Input: (batch, 1, T, H, W)
        batch_size = x.size(0)
        
        # Enhanced 3D CNN feature extraction
        x = torch.relu(self.bn3d_1(self.conv3d_1(x)))
        x = self.pool3d_1(x)
        
        x = torch.relu(self.bn3d_2(self.conv3d_2(x)))
        x = self.pool3d_2(x)
        
        x = torch.relu(self.bn3d_3(self.conv3d_3(x)))
        x = self.pool3d_3(x)
        
        x = torch.relu(self.bn3d_4(self.conv3d_4(x)))
        
        # Reshape for LSTM: (batch, time, features)
        # x shape: (batch, 512, 8, 7, 7)
        x = x.permute(0, 2, 1, 3, 4)  # (batch, 8, 512, 7, 7)
        x = x.contiguous().view(batch_size, x.size(1), -1)  # (batch, 8, 25088)
        
        # BiLSTM
        lstm_out, _ = self.lstm(x)  # (batch, 8, 1024)
        
        # Multi-head attention
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)  # (batch, 8, 1024)
        
        # Global average pooling over time dimension
        pooled = torch.mean(attn_out, dim=1)  # (batch, 1024)
        
        # Classification
        output = self.classifier(pooled)
        
        return output

class EnhancedTrainer:
    """Enhanced trainer with advanced optimization strategies"""
    
    def __init__(self, use_augmented_data=True):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  Using device: {self.device}")
        
        # Enhanced training parameters
        self.batch_size = 16  # Smaller batch for complex model
        self.learning_rate = 0.0005  # Lower learning rate for stability
        self.epochs = 50  # More epochs for complex model
        self.target_accuracy = 80.0
        self.patience = 15  # More patience for complex model
        
        # Create output directory
        self.output_dir = Path("artifacts/vsr_fasthead_v1")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup model and training
        self.use_augmented_data = use_augmented_data
        self.setup_model()
        self.setup_data_loaders()
        self.setup_training()
        
        # Training state
        self.best_accuracy = 0.0
        self.best_epoch = 0
        self.training_history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': [],
            'learning_rates': []
        }
    
    def setup_model(self):
        """Setup enhanced LipNet model"""
        print("🤖 Setting up Enhanced LipNet...")
        
        self.model = EnhancedLipNet(num_classes=5, dropout=0.4)
        self.model.to(self.device)
        
        # Print model info
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        print(f"   Total parameters: {total_params:,}")
        print(f"   Trainable parameters: {trainable_params:,}")
        print(f"   Architecture: Enhanced 3D CNN + BiLSTM + Attention")
    
    def setup_data_loaders(self):
        """Setup data loaders with optional TTA"""
        print("📊 Setting up enhanced data loaders...")
        
        if self.use_augmented_data:
            # Use augmented training dataset
            train_dataset = AugmentedDataset(
                "data/speaker_separated_augmented/augmented_manifest.csv",
                augment=False,  # Already augmented
                tta=False
            )
        else:
            # Use original processed dataset
            train_dataset = AugmentedDataset(
                "data/speaker_separated_processed/train_processed_manifest.csv",
                augment=True,
                tta=False
            )
        
        # Validation dataset with TTA
        val_dataset = AugmentedDataset(
            "data/speaker_separated_processed/val_processed_manifest.csv",
            augment=False,
            tta=True  # Enable TTA for more robust validation
        )
        
        # Create data loaders
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=4,
            pin_memory=True,
            drop_last=True
        )
        
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=2,
            pin_memory=True
        )
        
        print(f"   Training batches: {len(self.train_loader)}")
        print(f"   Validation batches: {len(self.val_loader)}")
    
    def setup_training(self):
        """Setup advanced training components"""
        print("⚙️  Setting up enhanced training components...")
        
        # AdamW optimizer with weight decay
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.learning_rate,
            weight_decay=0.01,
            betas=(0.9, 0.999)
        )
        
        # Reduce on plateau scheduler
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='max',  # Monitor validation accuracy
            factor=0.5,
            patience=5,
            verbose=True,
            min_lr=1e-6
        )
        
        # Focal loss for handling class imbalance
        self.criterion = nn.CrossEntropyLoss(label_smoothing=0.1)
        
        # Mixed precision training
        self.scaler = torch.cuda.amp.GradScaler() if self.device.type == 'cuda' else None
        
        print(f"   Optimizer: AdamW with weight decay")
        print(f"   Scheduler: ReduceLROnPlateau")
        print(f"   Loss: CrossEntropyLoss with label smoothing")
        print(f"   Mixed precision: {'Enabled' if self.scaler else 'Disabled'}")

    def train_epoch(self, epoch):
        """Train for one epoch with enhanced model"""
        self.model.train()

        total_loss = 0.0
        correct = 0
        total = 0

        pbar = tqdm(self.train_loader, desc=f"Enhanced Epoch {epoch+1}")

        for batch_idx, (videos, labels) in enumerate(pbar):
            videos, labels = videos.to(self.device), labels.to(self.device)

            self.optimizer.zero_grad()

            if self.scaler:
                with torch.cuda.amp.autocast():
                    outputs = self.model(videos)
                    loss = self.criterion(outputs, labels)

                self.scaler.scale(loss).backward()
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                outputs = self.model(videos)
                loss = self.criterion(outputs, labels)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                self.optimizer.step()

            # Statistics
            total_loss += loss.item()
            _, predicted = outputs.max(1)
            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()

            # Update progress bar
            current_lr = self.optimizer.param_groups[0]['lr']
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*correct/total:.2f}%',
                'LR': f'{current_lr:.6f}'
            })

        avg_loss = total_loss / len(self.train_loader)
        accuracy = 100. * correct / total

        return avg_loss, accuracy

    def validate(self):
        """Validate the enhanced model with TTA"""
        self.model.eval()

        total_loss = 0.0
        correct = 0
        total = 0
        all_predictions = []
        all_labels = []

        with torch.no_grad():
            for videos, labels in tqdm(self.val_loader, desc="Enhanced Validation"):
                videos, labels = videos.to(self.device), labels.to(self.device)

                if self.scaler:
                    with torch.cuda.amp.autocast():
                        outputs = self.model(videos)
                        loss = self.criterion(outputs, labels)
                else:
                    outputs = self.model(videos)
                    loss = self.criterion(outputs, labels)

                total_loss += loss.item()
                _, predicted = outputs.max(1)
                total += labels.size(0)
                correct += predicted.eq(labels).sum().item()

                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())

        avg_loss = total_loss / len(self.val_loader)
        accuracy = 100. * correct / total

        return avg_loss, accuracy, all_predictions, all_labels

    def save_checkpoint(self, epoch, accuracy, is_best=False):
        """Save enhanced model checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'accuracy': accuracy,
            'best_accuracy': self.best_accuracy,
            'training_history': self.training_history,
            'model_config': {
                'num_classes': 5,
                'dropout': 0.4,
                'architecture': 'EnhancedLipNet'
            }
        }

        # Save best model
        if is_best:
            best_path = self.output_dir / 'lipnet_5word_speaker_separated.pth'
            torch.save(checkpoint, best_path)
            print(f"💾 Enhanced model saved: {accuracy:.2f}% accuracy")

    def train(self):
        """Main enhanced training loop"""
        print("🚀 Starting Enhanced LipNet Training")
        print("=" * 70)

        start_time = time.time()
        epochs_without_improvement = 0

        for epoch in range(self.epochs):
            print(f"\n📅 Enhanced Epoch {epoch+1}/{self.epochs}")
            print("-" * 50)

            # Training
            epoch_start = time.time()
            train_loss, train_acc = self.train_epoch(epoch)

            # Validation
            val_loss, val_acc, predictions, labels = self.validate()

            # Update scheduler
            self.scheduler.step(val_acc)  # ReduceLROnPlateau needs metric
            current_lr = self.optimizer.param_groups[0]['lr']

            epoch_time = time.time() - epoch_start

            # Update training history
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_acc'].append(train_acc)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_acc'].append(val_acc)
            self.training_history['learning_rates'].append(current_lr)

            # Print epoch results
            print(f"\n📊 Enhanced Epoch {epoch+1} Results:")
            print(f"   Train Loss: {train_loss:.4f} | Train Acc: {train_acc:.2f}%")
            print(f"   Val Loss: {val_loss:.4f} | Val Acc: {val_acc:.2f}%")
            print(f"   Learning Rate: {current_lr:.6f}")
            print(f"   Time: {epoch_time:.1f}s")

            # Check if this is the best model
            is_best = val_acc > self.best_accuracy
            if is_best:
                self.best_accuracy = val_acc
                self.best_epoch = epoch
                epochs_without_improvement = 0
                print(f"🎉 NEW BEST: {val_acc:.2f}% validation accuracy!")

                # Print detailed classification report
                words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
                report = classification_report(labels, predictions, target_names=words, digits=3)
                print(f"\n📋 Enhanced Classification Report (Epoch {epoch+1}):")
                print(report)

            else:
                epochs_without_improvement += 1

            # Save checkpoint
            self.save_checkpoint(epoch, val_acc, is_best)

            # Check for target accuracy
            if val_acc >= self.target_accuracy:
                print(f"\n🎯 TARGET ACHIEVED: {self.target_accuracy:.1f}% validation accuracy!")
                print(f"   Final validation accuracy: {val_acc:.2f}%")
                print(f"   🏆 SUCCESS with Enhanced LipNet!")
                break

            # Early stopping
            if epochs_without_improvement >= self.patience:
                print(f"\n⏹️  Early stopping: No improvement for {self.patience} epochs")
                print(f"   Best accuracy: {self.best_accuracy:.2f}% at epoch {self.best_epoch+1}")
                break

            # Progress indicator
            if val_acc < self.target_accuracy:
                remaining = self.target_accuracy - val_acc
                print(f"   📈 Progress: {remaining:.1f}% to target")

        total_time = time.time() - start_time

        # Final results
        print(f"\n✅ Enhanced Training completed!")
        print(f"   Best validation accuracy: {self.best_accuracy:.2f}%")
        print(f"   Best epoch: {self.best_epoch+1}")
        print(f"   Target achieved: {'YES! 🎉' if self.best_accuracy >= self.target_accuracy else 'Not yet'}")
        print(f"   Total training time: {total_time/60:.1f} minutes")

        return self.best_accuracy >= self.target_accuracy

def main():
    """Main function to implement progressive improvements"""
    print("🚀 Enhanced LipNet Training for >80% Validation Accuracy")
    print("=" * 70)

    # Check if augmented data exists
    augmented_manifest = Path("data/speaker_separated_augmented/augmented_manifest.csv")
    if not augmented_manifest.exists():
        print("❌ Augmented dataset not found. Run aggressive_data_augmentation.py first!")
        return False

    print("✅ Simplified model plateaued at 40% - deploying Enhanced Architecture")
    print("🔧 Enhanced Features:")
    print("   • 3D CNN + BiLSTM + Multi-head Attention")
    print("   • Test Time Augmentation (TTA)")
    print("   • AdamW optimizer with ReduceLROnPlateau")
    print("   • Mixed precision training")
    print("   • Label smoothing + gradient clipping")

    # Create enhanced trainer
    enhanced_trainer = EnhancedTrainer(use_augmented_data=True)
    success = enhanced_trainer.train()

    if success:
        print(f"\n🎉 SUCCESS: >80% validation accuracy achieved with Enhanced LipNet!")
        print(f"📁 Model saved: {enhanced_trainer.output_dir}/lipnet_5word_speaker_separated.pth")
    else:
        print(f"\n⚠️  Enhanced model best: {enhanced_trainer.best_accuracy:.2f}%")
        print(f"💡 Consider further optimizations if needed")

    return success

if __name__ == "__main__":
    success = main()
