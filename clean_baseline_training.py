#!/usr/bin/env python3
"""
Phase 1: Clean baseline training with simple, stable configuration.
No ArcFace, no weighted sampler, no fancy schedulers - just what works.
Target: 40-50% val acc by epoch 5, 60-70% by epoch 15.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.models.video as video_models
import torchvision.transforms as transforms
import pandas as pd
import numpy as np
from pathlib import Path
import json
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, f1_score
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

class CleanDataset(Dataset):
    """Clean dataset with light augmentation and proper normalization"""
    
    def __init__(self, manifest_path, target_frames=16, augment=True, train_stats=None):
        self.manifest = pd.read_csv(manifest_path)
        self.target_frames = target_frames
        self.augment = augment
        
        # Verify all files exist
        valid_rows = []
        for idx, row in self.manifest.iterrows():
            if Path(row['video_path']).exists():
                valid_rows.append(row)
        
        self.manifest = pd.DataFrame(valid_rows).reset_index(drop=True)
        
        print(f"📊 Dataset loaded: {len(self.manifest)} videos")
        class_counts = self.manifest['word'].value_counts()
        print(f"   Class distribution: {dict(class_counts)}")
        
        # Compute dataset statistics if not provided
        if train_stats is None:
            self.compute_dataset_stats()
        else:
            self.mean, self.std = train_stats
        
        print(f"   Using normalization - Mean: {self.mean:.4f}, Std: {self.std:.4f}")
        
        # Light augmentation for training
        if augment:
            self.spatial_transforms = transforms.Compose([
                transforms.RandomResizedCrop(size=(112, 112), scale=(0.9, 1.0)),
                transforms.ColorJitter(brightness=0.1, contrast=0.1),
            ])
        else:
            self.spatial_transforms = transforms.Resize((112, 112))
    
    def compute_dataset_stats(self):
        """Compute mean and std from the dataset"""
        print("🔄 Computing dataset statistics...")
        all_pixels = []
        
        sample_size = min(50, len(self.manifest))
        for idx in tqdm(range(sample_size), desc="Computing stats"):
            row = self.manifest.iloc[idx]
            try:
                video_tensor = torch.load(row['video_path'])
                
                if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
                    video_tensor = video_tensor.squeeze(0)
                
                all_pixels.append(video_tensor.flatten())
            except Exception as e:
                print(f"Error loading {row['video_path']}: {e}")
                continue
        
        if all_pixels:
            all_pixels = torch.cat(all_pixels)
            self.mean = all_pixels.mean().item()
            self.std = all_pixels.std().item()
        else:
            self.mean, self.std = 0.0, 1.0
    
    def __len__(self):
        return len(self.manifest)
    
    def __getitem__(self, idx):
        row = self.manifest.iloc[idx]
        
        # Load preprocessed tensor
        try:
            video_tensor = torch.load(row['video_path'])
        except Exception as e:
            print(f"Error loading {row['video_path']}: {e}")
            # Return dummy tensor
            video_tensor = torch.randn(1, 64, 112, 112)
        
        # Ensure correct shape
        if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
            video_tensor = video_tensor.squeeze(0)
        elif video_tensor.dim() == 3:
            pass
        else:
            print(f"Unexpected tensor shape: {video_tensor.shape}")
            video_tensor = torch.randn(64, 112, 112)
        
        # Temporal sampling with light augmentation
        T, H, W = video_tensor.shape
        if T >= self.target_frames:
            if self.augment and np.random.random() > 0.7:  # 30% chance of random crop
                start_idx = np.random.randint(0, max(1, T - self.target_frames))
                indices = torch.arange(start_idx, start_idx + self.target_frames)
            else:
                # Uniform sampling
                indices = torch.linspace(0, T - 1, self.target_frames).long()
            video_tensor = video_tensor[indices]
        else:
            # Repeat frames to reach target
            repeat_factor = (self.target_frames + T - 1) // T
            video_tensor = video_tensor.repeat(repeat_factor, 1, 1)[:self.target_frames]
        
        # Convert to 3-channel by repeating grayscale
        video_tensor = video_tensor.unsqueeze(1).repeat(1, 3, 1, 1)
        
        # Normalize from [-1, 1] to [0, 1] first
        video_tensor = (video_tensor + 1.0) / 2.0
        
        # Apply light spatial augmentation
        if self.augment and np.random.random() > 0.5:  # 50% chance
            augmented_frames = []
            for t in range(self.target_frames):
                frame = video_tensor[t]
                frame = self.spatial_transforms(frame)
                augmented_frames.append(frame)
            video_tensor = torch.stack(augmented_frames)
        else:
            video_tensor = torch.stack([transforms.Resize((112, 112))(video_tensor[t]) for t in range(self.target_frames)])
        
        # Normalize with dataset statistics
        video_tensor = (video_tensor - self.mean) / self.std
        
        # Rearrange to (3, T, H, W) for R(2+1)D
        video_tensor = video_tensor.permute(1, 0, 2, 3)
        
        label = torch.tensor(row['label'], dtype=torch.long)
        
        return video_tensor, label

class CleanR2Plus1DModel(nn.Module):
    """Clean R(2+1)D model with simple linear classifier"""
    
    def __init__(self, num_classes=5, dropout=0.3):
        super().__init__()
        
        # Load pretrained R(2+1)D-18
        try:
            self.backbone = video_models.r2plus1d_18(weights='KINETICS400_V1')
        except TypeError:
            self.backbone = video_models.r2plus1d_18(pretrained=True)
        
        # Remove final classifier
        self.backbone.fc = nn.Identity()
        
        # Simple classifier with light dropout
        self.classifier = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(512, num_classes)
        )
        
        print(f"🤖 CleanR2Plus1DModel created")
        print(f"   Backbone: R(2+1)D-18")
        print(f"   Classifier: Linear with dropout {dropout}")
        print(f"   Output classes: {num_classes}")
    
    def forward(self, x):
        # x: (batch, 3, T, H, W)
        features = self.backbone(x)  # (batch, 512)
        logits = self.classifier(features)
        return logits

class CleanTrainer:
    """Clean trainer with stable configuration"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  Using device: {self.device}")
        
        self.batch_size = 16  # Larger batch for stability
        
        # Create output directory
        self.output_dir = Path("artifacts/clean_baseline_v1")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🎯 Clean Baseline Training:")
        print(f"   Batch size: {self.batch_size}")
        print(f"   Simple configuration - no fancy features")
    
    def create_data_loaders(self, train_manifest, val_manifest):
        """Create clean data loaders"""
        
        # Training dataset with light augmentation
        train_dataset = CleanDataset(
            train_manifest,
            target_frames=16,
            augment=True
        )
        
        # Get training stats for validation normalization
        train_stats = (train_dataset.mean, train_dataset.std)
        
        # Validation dataset without augmentation
        val_dataset = CleanDataset(
            val_manifest,
            target_frames=16,
            augment=False,
            train_stats=train_stats
        )
        
        # Create data loaders with standard shuffling
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            shuffle=True,  # Standard shuffle, no weighted sampler
            num_workers=2,
            pin_memory=True,
            drop_last=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=2,
            pin_memory=True
        )
        
        return train_loader, val_loader, train_dataset
    
    def train_model(self, model, train_loader, val_loader, train_dataset):
        """Clean training with stable configuration"""
        print("\n🚀 Clean Baseline Training")
        print("=" * 50)
        
        # Get class counts for class weights
        labels = [train_dataset.manifest.iloc[i]['label'] for i in range(len(train_dataset))]
        class_counts = np.bincount(labels)
        
        # Compute modest class weights (inverse frequency)
        class_weights = 1.0 / class_counts
        class_weights = class_weights / class_weights.sum() * len(class_weights)
        class_weights = torch.tensor(class_weights, dtype=torch.float32, device=self.device)
        
        print(f"📊 Class counts: {class_counts}")
        print(f"📊 Class weights: {class_weights.cpu().numpy()}")
        
        # Simple optimizer - NO scheduler
        optimizer = optim.AdamW(
            model.parameters(),
            lr=3e-4,  # Fixed learning rate
            weight_decay=1e-4,
            betas=(0.9, 0.999)
        )
        
        # CrossEntropy with modest class weights
        criterion = nn.CrossEntropyLoss(weight=class_weights)
        
        best_val_acc = 0.0
        best_macro_f1 = 0.0
        epochs = 30
        
        words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
        
        for epoch in range(epochs):
            print(f"\n📅 Epoch {epoch+1}/{epochs}")
            
            # Training phase
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for batch_idx, (videos, labels) in enumerate(tqdm(train_loader, desc="Training")):
                videos, labels = videos.to(self.device), labels.to(self.device)
                
                optimizer.zero_grad()
                logits = model(videos)
                loss = criterion(logits, labels)
                loss.backward()
                
                # Check gradient norms
                total_norm = torch.sqrt(sum((p.grad.detach()**2).sum() for p in model.parameters() if p.grad is not None))
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()
                
                train_loss += loss.item()
                pred = logits.argmax(1)
                train_total += labels.size(0)
                train_correct += (pred == labels).sum().item()
                
                # Print first batch details for debugging
                if batch_idx == 0:
                    print(f"   Batch 0: loss={loss.item():.3f}, grad_norm={total_norm.item():.3f}")
                    print(f"   Labels: {labels.cpu().numpy()}")
                    print(f"   Preds:  {pred.cpu().numpy()}")
            
            # Validation phase
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            all_predictions = []
            all_labels = []
            
            with torch.no_grad():
                for videos, labels in tqdm(val_loader, desc="Validation"):
                    videos, labels = videos.to(self.device), labels.to(self.device)
                    
                    logits = model(videos)
                    loss = criterion(logits, labels)
                    
                    val_loss += loss.item()
                    pred = logits.argmax(1)
                    val_total += labels.size(0)
                    val_correct += (pred == labels).sum().item()
                    
                    all_predictions.extend(pred.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())
            
            # Calculate metrics
            train_acc = 100. * train_correct / train_total
            val_acc = 100. * val_correct / val_total
            macro_f1 = f1_score(all_labels, all_predictions, average='macro') * 100
            
            print(f"📊 Results:")
            print(f"   Train Loss: {train_loss/len(train_loader):.4f} | Train Acc: {train_acc:.2f}%")
            print(f"   Val Loss: {val_loss/len(val_loader):.4f} | Val Acc: {val_acc:.2f}%")
            print(f"   Macro F1: {macro_f1:.2f}%")
            
            # Confusion matrix
            cm = confusion_matrix(all_labels, all_predictions, labels=list(range(5)))
            print(f"\n🔍 Confusion Matrix (Epoch {epoch+1}):")
            print("     ", " ".join([f"{word:>8}" for word in words]))
            for i, word in enumerate(words):
                print(f"{word:>8}: {' '.join([f'{cm[i,j]:>8}' for j in range(len(words))])}")
            
            # Per-class accuracy
            per_class_acc = cm.diagonal() / cm.sum(axis=1) * 100
            print(f"\n📊 Per-class Accuracy:")
            for i, word in enumerate(words):
                print(f"   {word}: {per_class_acc[i]:.1f}%")
            
            # Check for improvement
            improved = val_acc > best_val_acc
            
            if improved:
                best_val_acc = val_acc
                best_macro_f1 = max(best_macro_f1, macro_f1)
                
                # Save best model
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'val_accuracy': val_acc,
                    'macro_f1': macro_f1,
                    'training_config': {
                        'batch_size': self.batch_size,
                        'learning_rate': 3e-4,
                        'architecture': 'Clean_R2Plus1D',
                        'class_weights': True,
                        'augmentation': 'light'
                    }
                }, self.output_dir / 'clean_baseline_best.pth')
                
                print(f"🎉 NEW BEST: {val_acc:.2f}% validation accuracy!")
                
                # Detailed classification report
                report = classification_report(all_labels, all_predictions, target_names=words, digits=3)
                print(f"\n📋 Classification Report:")
                print(report)
            
            # Check milestones
            if epoch == 4 and val_acc < 40:
                print(f"\n⚠️  Milestone check: Val acc {val_acc:.2f}% < 40% at epoch 5")
            elif epoch == 14 and val_acc < 60:
                print(f"\n⚠️  Milestone check: Val acc {val_acc:.2f}% < 60% at epoch 15")
        
        print(f"\n✅ Clean baseline training completed!")
        print(f"   Best validation accuracy: {best_val_acc:.2f}%")
        print(f"   Best macro F1: {best_macro_f1:.2f}%")
        
        return best_val_acc, best_macro_f1

def create_proper_train_val_split():
    """Create a proper 70/30 train/validation split"""
    print("🔄 Creating proper train/validation split...")
    
    # Use existing processed data
    train_manifest = "data/5word_processed/train_processed_manifest.csv"
    val_manifest = "data/5word_processed/val_processed_manifest.csv"
    
    if Path(train_manifest).exists() and Path(val_manifest).exists():
        train_df = pd.read_csv(train_manifest)
        val_df = pd.read_csv(val_manifest)
        all_df = pd.concat([train_df, val_df], ignore_index=True)
    else:
        print("❌ Processed manifests not found")
        return None, None
    
    print(f"📊 Total available data: {len(all_df)} videos")
    class_counts = all_df['word'].value_counts()
    print(f"   Class distribution: {dict(class_counts)}")
    
    # Filter out male 18-39 if demographic info available
    if 'filename' in all_df.columns:
        filtered_df = all_df[~all_df['filename'].str.contains('18to39.*male', case=False, na=False)]
        print(f"📊 After filtering male 18-39: {len(filtered_df)}")
    else:
        filtered_df = all_df
    
    # Create stratified split
    X = filtered_df.drop(['label'], axis=1)
    y = filtered_df['label']
    
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, 
        test_size=0.3,
        stratify=y,
        random_state=42
    )
    
    # Recreate dataframes
    train_df = pd.concat([X_train, y_train], axis=1)
    val_df = pd.concat([X_val, y_val], axis=1)
    
    # Save new splits
    train_df.to_csv("data/clean_train_manifest.csv", index=False)
    val_df.to_csv("data/clean_val_manifest.csv", index=False)
    
    print(f"✅ Clean split created:")
    print(f"   Training: {len(train_df)} videos")
    print(f"   Validation: {len(val_df)} videos")
    
    return "data/clean_train_manifest.csv", "data/clean_val_manifest.csv"

def main():
    """Main function for clean baseline training"""
    print("🎯 Phase 1: Clean Baseline Training")
    print("=" * 60)
    
    # Step 1: Create proper train/validation split
    train_manifest, val_manifest = create_proper_train_val_split()
    
    if train_manifest is None:
        print("❌ Failed to create train/val split")
        return False
    
    # Step 2: Create clean trainer
    trainer = CleanTrainer()
    
    # Step 3: Create data loaders
    train_loader, val_loader, train_dataset = trainer.create_data_loaders(train_manifest, val_manifest)
    
    # Step 4: Create clean model
    model = CleanR2Plus1DModel(num_classes=5, dropout=0.3)
    model.to(trainer.device)
    
    total_params = sum(p.numel() for p in model.parameters())
    print(f"📊 Model parameters: {total_params:,}")
    
    # Step 5: Train model
    final_acc, final_f1 = trainer.train_model(model, train_loader, val_loader, train_dataset)
    
    print(f"\n📊 Final results: {final_acc:.2f}% val acc, {final_f1:.2f}% macro F1")
    
    if final_acc >= 60.0:
        print("✅ Clean baseline successful - ready for Phase 2 enhancements")
        return True
    else:
        print("⚠️  Clean baseline needs improvement before adding complexity")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
