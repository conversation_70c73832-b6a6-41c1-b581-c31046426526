#!/usr/bin/env python3
"""
Cross-validation LipNet training to overcome small validation set issues.
Implement k-fold cross-validation with the original 70 training videos.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset, Subset
import pandas as pd
import numpy as np
from pathlib import Path
import json
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, classification_report
from sklearn.model_selection import StratifiedKFold
import warnings
warnings.filterwarnings('ignore')

class OriginalDataset(Dataset):
    """Dataset using original 70 training videos for cross-validation"""
    
    def __init__(self, manifest_path, light_augment=False):
        self.manifest = pd.read_csv(manifest_path)
        self.light_augment = light_augment
        
        # Verify all files exist
        valid_rows = []
        for idx, row in self.manifest.iterrows():
            if Path(row['video_path']).exists():
                valid_rows.append(row)
        
        self.manifest = pd.DataFrame(valid_rows).reset_index(drop=True)
        
        print(f"📊 Original dataset loaded: {len(self.manifest)} videos")
        class_counts = self.manifest['word'].value_counts()
        print(f"   Class distribution: {dict(class_counts)}")
        speaker_counts = self.manifest['speaker'].value_counts()
        print(f"   Speakers: {len(speaker_counts)} unique speakers")
    
    def __len__(self):
        return len(self.manifest)
    
    def apply_light_augmentation(self, video_tensor):
        """Apply very light augmentation to prevent overfitting"""
        augmented = video_tensor.clone()
        
        # Very light brightness adjustment
        if torch.rand(1) < 0.3:
            brightness = (torch.rand(1) - 0.5) * 0.1  # ±5% brightness
            augmented = augmented + brightness
        
        # Light horizontal flip
        if torch.rand(1) < 0.3:
            augmented = torch.flip(augmented, dims=[-1])
        
        # Tiny amount of noise
        if torch.rand(1) < 0.2:
            noise = torch.randn_like(augmented) * 0.005
            augmented = augmented + noise
        
        return torch.clamp(augmented, -3, 3)
    
    def __getitem__(self, idx):
        row = self.manifest.iloc[idx]
        
        # Load preprocessed tensor
        video_tensor = torch.load(row['video_path'])
        
        # Ensure correct shape: (1, T, H, W)
        if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
            pass  # Already correct
        elif video_tensor.dim() == 3:
            video_tensor = video_tensor.unsqueeze(0)
        else:
            raise ValueError(f"Unexpected tensor shape: {video_tensor.shape}")
        
        # Apply light augmentation if enabled
        if self.light_augment:
            video_tensor = self.apply_light_augmentation(video_tensor)
        
        label = torch.tensor(row['label'], dtype=torch.long)
        
        return video_tensor, label

class RegularizedLipNet(nn.Module):
    """Regularized LipNet with strong regularization to prevent overfitting"""
    
    def __init__(self, num_classes=5, dropout=0.6):
        super(RegularizedLipNet, self).__init__()
        
        # Smaller 3D CNN to reduce overfitting
        self.conv3d_1 = nn.Conv3d(1, 32, kernel_size=(3, 7, 7), stride=(1, 2, 2), padding=(1, 3, 3))
        self.bn3d_1 = nn.BatchNorm3d(32)
        self.pool3d_1 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        self.dropout3d_1 = nn.Dropout3d(0.2)
        
        self.conv3d_2 = nn.Conv3d(32, 64, kernel_size=(3, 5, 5), stride=(1, 1, 1), padding=(1, 2, 2))
        self.bn3d_2 = nn.BatchNorm3d(64)
        self.pool3d_2 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        self.dropout3d_2 = nn.Dropout3d(0.3)
        
        self.conv3d_3 = nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))
        self.bn3d_3 = nn.BatchNorm3d(128)
        self.pool3d_3 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        self.dropout3d_3 = nn.Dropout3d(0.4)
        
        # Calculate LSTM input size: 128 * 7 * 7 = 6272
        self.lstm_input_size = 128 * 7 * 7
        
        # Smaller BiLSTM to reduce parameters
        self.lstm = nn.LSTM(
            input_size=self.lstm_input_size,
            hidden_size=128,  # Reduced from 512
            num_layers=1,     # Reduced from 2
            batch_first=True,
            dropout=0.0,      # No dropout in LSTM due to single layer
            bidirectional=True
        )
        
        # Simple attention
        self.attention = nn.MultiheadAttention(
            embed_dim=256,    # 128 * 2 (bidirectional)
            num_heads=4,      # Reduced from 16
            dropout=dropout,
            batch_first=True
        )
        
        # Heavily regularized classification head
        self.classifier = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(128),
            nn.Dropout(dropout),
            nn.Linear(128, 64),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(64),
            nn.Dropout(dropout),
            nn.Linear(64, num_classes)
        )
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize model weights"""
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm3d) or isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LSTM):
                for name, param in m.named_parameters():
                    if 'weight' in name:
                        nn.init.orthogonal_(param)
                    elif 'bias' in name:
                        nn.init.constant_(param, 0)
    
    def forward(self, x):
        # Input: (batch, 1, T, H, W)
        batch_size = x.size(0)
        
        # 3D CNN with heavy regularization
        x = torch.relu(self.bn3d_1(self.conv3d_1(x)))
        x = self.pool3d_1(x)
        x = self.dropout3d_1(x)
        
        x = torch.relu(self.bn3d_2(self.conv3d_2(x)))
        x = self.pool3d_2(x)
        x = self.dropout3d_2(x)
        
        x = torch.relu(self.bn3d_3(self.conv3d_3(x)))
        x = self.pool3d_3(x)
        x = self.dropout3d_3(x)
        
        # Reshape for LSTM: (batch, time, features)
        x = x.permute(0, 2, 1, 3, 4)  # (batch, 8, 128, 7, 7)
        x = x.contiguous().view(batch_size, x.size(1), -1)  # (batch, 8, 6272)
        
        # BiLSTM
        lstm_out, _ = self.lstm(x)  # (batch, 8, 256)
        
        # Attention
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)  # (batch, 8, 256)
        
        # Global average pooling over time dimension
        pooled = torch.mean(attn_out, dim=1)  # (batch, 256)
        
        # Classification
        output = self.classifier(pooled)
        
        return output

class CrossValidationTrainer:
    """Cross-validation trainer for small dataset"""
    
    def __init__(self, k_folds=5):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  Using device: {self.device}")
        
        self.k_folds = k_folds
        self.batch_size = 8  # Small batch for small dataset
        self.learning_rate = 0.0001  # Very low learning rate
        self.epochs = 30
        self.target_accuracy = 80.0
        
        # Create output directory
        self.output_dir = Path("artifacts/vsr_fasthead_v1")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🔄 Cross-validation setup:")
        print(f"   K-folds: {k_folds}")
        print(f"   Batch size: {self.batch_size}")
        print(f"   Learning rate: {self.learning_rate}")
        print(f"   Target accuracy: {self.target_accuracy}%")
    
    def create_model(self):
        """Create regularized model"""
        model = RegularizedLipNet(num_classes=5, dropout=0.6)
        model.to(self.device)
        
        total_params = sum(p.numel() for p in model.parameters())
        print(f"   Model parameters: {total_params:,}")
        
        return model
    
    def train_fold(self, model, train_loader, val_loader, fold):
        """Train a single fold"""
        print(f"\n🔄 Training Fold {fold+1}")
        print("-" * 40)
        
        # Optimizer with strong weight decay
        optimizer = optim.AdamW(
            model.parameters(),
            lr=self.learning_rate,
            weight_decay=0.1,  # Very strong weight decay
            betas=(0.9, 0.999)
        )
        
        # Scheduler
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=self.epochs,
            eta_min=1e-6
        )
        
        # Loss function
        criterion = nn.CrossEntropyLoss(label_smoothing=0.2)  # Strong label smoothing
        
        best_val_acc = 0.0
        patience = 10
        epochs_without_improvement = 0
        
        for epoch in range(self.epochs):
            # Training
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for videos, labels in train_loader:
                videos, labels = videos.to(self.device), labels.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(videos)
                loss = criterion(outputs, labels)
                loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(model.parameters(), 0.5)
                
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = outputs.max(1)
                train_total += labels.size(0)
                train_correct += predicted.eq(labels).sum().item()
            
            # Validation
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            
            with torch.no_grad():
                for videos, labels in val_loader:
                    videos, labels = videos.to(self.device), labels.to(self.device)
                    
                    outputs = model(videos)
                    loss = criterion(outputs, labels)
                    
                    val_loss += loss.item()
                    _, predicted = outputs.max(1)
                    val_total += labels.size(0)
                    val_correct += predicted.eq(labels).sum().item()
            
            scheduler.step()
            
            train_acc = 100. * train_correct / train_total
            val_acc = 100. * val_correct / val_total
            
            print(f"   Epoch {epoch+1:2d}: Train {train_acc:5.1f}% | Val {val_acc:5.1f}%")
            
            # Check improvement
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                epochs_without_improvement = 0
            else:
                epochs_without_improvement += 1
            
            # Early stopping
            if epochs_without_improvement >= patience:
                print(f"   Early stopping at epoch {epoch+1}")
                break
        
        print(f"   Fold {fold+1} best validation accuracy: {best_val_acc:.2f}%")
        return best_val_acc
    
    def cross_validate(self):
        """Perform k-fold cross-validation"""
        print("🚀 Starting Cross-Validation Training")
        print("=" * 70)
        
        # Load original dataset (70 videos)
        dataset = OriginalDataset(
            "data/speaker_separated_processed/train_processed_manifest.csv",
            light_augment=True
        )
        
        # Get labels for stratified split
        labels = [dataset[i][1].item() for i in range(len(dataset))]
        
        # Create stratified k-fold
        skf = StratifiedKFold(n_splits=self.k_folds, shuffle=True, random_state=42)
        
        fold_accuracies = []
        
        for fold, (train_idx, val_idx) in enumerate(skf.split(range(len(dataset)), labels)):
            print(f"\n📊 Fold {fold+1}/{self.k_folds}")
            print(f"   Training samples: {len(train_idx)}")
            print(f"   Validation samples: {len(val_idx)}")
            
            # Create data loaders
            train_subset = Subset(dataset, train_idx)
            val_subset = Subset(dataset, val_idx)
            
            train_loader = DataLoader(
                train_subset,
                batch_size=self.batch_size,
                shuffle=True,
                num_workers=2,
                pin_memory=True
            )
            
            val_loader = DataLoader(
                val_subset,
                batch_size=self.batch_size,
                shuffle=False,
                num_workers=2,
                pin_memory=True
            )
            
            # Create and train model
            model = self.create_model()
            fold_acc = self.train_fold(model, train_loader, val_loader, fold)
            fold_accuracies.append(fold_acc)
        
        # Calculate overall results
        mean_acc = np.mean(fold_accuracies)
        std_acc = np.std(fold_accuracies)
        
        print(f"\n📊 Cross-Validation Results:")
        print("=" * 50)
        for i, acc in enumerate(fold_accuracies):
            print(f"   Fold {i+1}: {acc:.2f}%")
        print(f"   Mean: {mean_acc:.2f}% ± {std_acc:.2f}%")
        
        # Save results
        results = {
            'fold_accuracies': fold_accuracies,
            'mean_accuracy': mean_acc,
            'std_accuracy': std_acc,
            'target_achieved': mean_acc >= self.target_accuracy,
            'k_folds': self.k_folds,
            'training_config': {
                'batch_size': self.batch_size,
                'learning_rate': self.learning_rate,
                'epochs': self.epochs,
                'regularization': 'heavy'
            }
        }
        
        results_path = self.output_dir / 'cross_validation_results.json'
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"📄 Results saved: {results_path}")
        
        if mean_acc >= self.target_accuracy:
            print(f"\n🎉 SUCCESS: Cross-validation achieved {mean_acc:.2f}% > {self.target_accuracy}%!")
            return True
        else:
            print(f"\n⚠️  Cross-validation: {mean_acc:.2f}% < {self.target_accuracy}%")
            print(f"💡 Need further optimizations")
            return False

def main():
    """Main cross-validation function"""
    print("🎯 Cross-Validation LipNet Training")
    print("=" * 60)
    
    # Check if original processed data exists
    original_manifest = Path("data/speaker_separated_processed/train_processed_manifest.csv")
    if not original_manifest.exists():
        print("❌ Original processed dataset not found!")
        return False
    
    # Create cross-validation trainer
    cv_trainer = CrossValidationTrainer(k_folds=5)
    success = cv_trainer.cross_validate()
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
