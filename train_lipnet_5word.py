#!/usr/bin/env python3
"""
Train LipNet for 5-word ICU classification task.
Adapted from existing LipNet implementation for doctor, glasses, help, phone, pillow.
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import pandas as pd
import numpy as np
from pathlib import Path
import json
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# Import existing LipNet components
from lipnet_perfect_10 import LipNetPerfect10, LipNet3DCNN

class LipNet5WordDataset(Dataset):
    """Dataset for 5-word LipNet training using preprocessed .pt files"""
    
    def __init__(self, manifest_path, augment=False):
        self.manifest = pd.read_csv(manifest_path)
        self.augment = augment
        
        # Verify all files exist
        valid_rows = []
        for idx, row in self.manifest.iterrows():
            if Path(row['video_path']).exists():
                valid_rows.append(row)
            else:
                print(f"⚠️  Missing file: {row['video_path']}")
        
        self.manifest = pd.DataFrame(valid_rows).reset_index(drop=True)
        print(f"📊 Dataset loaded: {len(self.manifest)} videos")
        
        # Print class distribution
        class_counts = self.manifest['word'].value_counts()
        print(f"   Class distribution: {dict(class_counts)}")
    
    def __len__(self):
        return len(self.manifest)
    
    def __getitem__(self, idx):
        row = self.manifest.iloc[idx]
        
        # Load preprocessed tensor
        video_tensor = torch.load(row['video_path'])
        
        # Apply augmentation if enabled
        if self.augment:
            video_tensor = self.apply_augmentation(video_tensor)
        
        label = torch.tensor(row['label'], dtype=torch.long)
        
        return video_tensor, label
    
    def apply_augmentation(self, video_tensor):
        """Apply data augmentation to video tensor"""
        # video_tensor shape: (1, num_frames, height, width)
        
        # Random brightness/contrast
        if torch.rand(1) < 0.5:
            brightness = torch.rand(1) * 0.4 - 0.2  # -0.2 to +0.2
            contrast = torch.rand(1) * 0.4 + 0.8    # 0.8 to 1.2
            video_tensor = video_tensor * contrast + brightness
        
        # Random horizontal flip
        if torch.rand(1) < 0.5:
            video_tensor = torch.flip(video_tensor, dims=[-1])
        
        # Random temporal dropout (drop some frames)
        if torch.rand(1) < 0.3:
            num_frames = video_tensor.size(1)
            keep_frames = int(num_frames * 0.9)  # Keep 90% of frames
            frame_indices = torch.randperm(num_frames)[:keep_frames]
            frame_indices = torch.sort(frame_indices)[0]
            
            # Pad to original length by repeating last frame
            while len(frame_indices) < num_frames:
                frame_indices = torch.cat([frame_indices, frame_indices[-1:]])
            
            video_tensor = video_tensor[:, frame_indices]
        
        # Add Gaussian noise
        if torch.rand(1) < 0.3:
            noise = torch.randn_like(video_tensor) * 0.02
            video_tensor = video_tensor + noise
        
        return video_tensor

class LipNet5WordTrainer:
    """Trainer for LipNet 5-word classification"""
    
    def __init__(self, config_path):
        self.config = self.load_config(config_path)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  Using device: {self.device}")
        
        # Create output directories
        self.setup_directories()
        
        # Initialize model, data loaders, optimizer, etc.
        self.setup_model()
        self.setup_data_loaders()
        self.setup_training()
        
        # Training state
        self.best_accuracy = 0.0
        self.training_history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': []
        }
    
    def load_config(self, config_path):
        """Load configuration from YAML file"""
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        return config
    
    def setup_directories(self):
        """Create output directories"""
        output_dir = Path(self.config['paths']['output_dir'])
        output_dir.mkdir(parents=True, exist_ok=True)
        
        checkpoint_dir = Path(self.config['paths']['checkpoint_dir'])
        checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        logs_dir = Path(self.config['paths']['logs_dir'])
        logs_dir.mkdir(parents=True, exist_ok=True)
    
    def setup_model(self):
        """Setup LipNet model for 5-word classification"""
        print("🤖 Setting up LipNet model...")
        
        # Create LipNet model adapted for 5-word classification
        self.model = LipNetPerfect10(
            num_classes=self.config['model']['num_classes'],
            hidden_dim=self.config['model']['hidden_dim'],
            num_rnn_layers=self.config['model']['num_rnn_layers'],
            rnn_type=self.config['model']['rnn_type'],
            dropout=self.config['model']['dropout']
        )
        
        self.model.to(self.device)
        
        # Freeze encoder if specified
        if self.config['model']['freeze_encoder']:
            print("🔒 Freezing 3D CNN encoder...")
            for param in self.model.cnn_backbone.parameters():
                param.requires_grad = False
        
        # Print model info
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        print(f"   Total parameters: {total_params:,}")
        print(f"   Trainable parameters: {trainable_params:,}")
        print(f"   Architecture: 3D CNN + BiLSTM + Attention")
    
    def setup_data_loaders(self):
        """Setup data loaders"""
        print("📊 Setting up data loaders...")
        
        # Training dataset with augmentation
        train_dataset = LipNet5WordDataset(
            self.config['dataset']['train_manifest'],
            augment=True
        )
        
        # Validation dataset without augmentation
        val_dataset = LipNet5WordDataset(
            self.config['dataset']['val_manifest'],
            augment=False
        )
        
        # Create data loaders
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=True,
            num_workers=self.config['dataset']['num_workers'],
            pin_memory=self.config['dataset']['pin_memory']
        )
        
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=False,
            num_workers=self.config['dataset']['num_workers'],
            pin_memory=self.config['dataset']['pin_memory']
        )
        
        print(f"   Training batches: {len(self.train_loader)}")
        print(f"   Validation batches: {len(self.val_loader)}")
    
    def setup_training(self):
        """Setup optimizer, scheduler, and loss function"""
        print("⚙️  Setting up training components...")
        
        # Optimizer
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.config['optimizer']['lr'],
            weight_decay=self.config['optimizer']['weight_decay'],
            betas=self.config['optimizer']['betas']
        )
        
        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer,
            T_0=self.config['scheduler']['T_0'],
            T_mult=self.config['scheduler']['T_mult'],
            eta_min=self.config['scheduler']['eta_min']
        )
        
        # Loss function
        self.criterion = nn.CrossEntropyLoss(
            label_smoothing=self.config['loss']['label_smoothing']
        )
        
        # Mixed precision scaler
        if self.config['amp']:
            self.scaler = torch.cuda.amp.GradScaler()
        else:
            self.scaler = None
    
    def train_epoch(self, epoch):
        """Train for one epoch"""
        self.model.train()
        
        total_loss = 0.0
        correct = 0
        total = 0
        
        pbar = tqdm(self.train_loader, desc=f"Epoch {epoch+1}")
        
        for batch_idx, (videos, labels) in enumerate(pbar):
            videos, labels = videos.to(self.device), labels.to(self.device)
            
            self.optimizer.zero_grad()
            
            if self.scaler:
                with torch.cuda.amp.autocast():
                    outputs = self.model(videos)
                    loss = self.criterion(outputs, labels)
                
                self.scaler.scale(loss).backward()
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                outputs = self.model(videos)
                loss = self.criterion(outputs, labels)
                loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(),
                    self.config['training']['gradient_clip_norm']
                )
                
                self.optimizer.step()
            
            # Statistics
            total_loss += loss.item()
            _, predicted = outputs.max(1)
            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()
            
            # Update progress bar
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*correct/total:.2f}%'
            })
        
        # Update scheduler
        self.scheduler.step()
        
        avg_loss = total_loss / len(self.train_loader)
        accuracy = 100. * correct / total
        
        return avg_loss, accuracy
    
    def validate(self):
        """Validate the model"""
        self.model.eval()
        
        total_loss = 0.0
        correct = 0
        total = 0
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            for videos, labels in tqdm(self.val_loader, desc="Validating"):
                videos, labels = videos.to(self.device), labels.to(self.device)
                
                outputs = self.model(videos)
                loss = self.criterion(outputs, labels)
                
                total_loss += loss.item()
                _, predicted = outputs.max(1)
                total += labels.size(0)
                correct += predicted.eq(labels).sum().item()
                
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        avg_loss = total_loss / len(self.val_loader)
        accuracy = 100. * correct / total
        
        return avg_loss, accuracy, all_predictions, all_labels

    def save_checkpoint(self, epoch, accuracy, is_best=False):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'accuracy': accuracy,
            'config': self.config
        }

        # Save regular checkpoint
        checkpoint_path = Path(self.config['paths']['checkpoint_dir']) / f'checkpoint_epoch_{epoch+1}.pth'
        torch.save(checkpoint, checkpoint_path)

        # Save best model
        if is_best:
            best_path = Path(self.config['paths']['best_model_path'])
            torch.save(checkpoint, best_path)
            print(f"💾 Saved best model: {accuracy:.2f}% accuracy")

    def plot_training_history(self):
        """Plot training history"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))

        # Loss plot
        ax1.plot(self.training_history['train_loss'], label='Train Loss')
        ax1.plot(self.training_history['val_loss'], label='Val Loss')
        ax1.set_title('Training and Validation Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)

        # Accuracy plot
        ax2.plot(self.training_history['train_acc'], label='Train Acc')
        ax2.plot(self.training_history['val_acc'], label='Val Acc')
        ax2.set_title('Training and Validation Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy (%)')
        ax2.legend()
        ax2.grid(True)

        plt.tight_layout()
        plot_path = Path(self.config['paths']['output_dir']) / 'training_history.png'
        plt.savefig(plot_path, dpi=150, bbox_inches='tight')
        plt.close()

        print(f"📊 Training history saved: {plot_path}")

    def plot_confusion_matrix(self, predictions, labels, epoch):
        """Plot confusion matrix"""
        phrases = self.config['phrases']
        cm = confusion_matrix(labels, predictions)

        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=phrases, yticklabels=phrases)
        plt.title(f'Confusion Matrix - Epoch {epoch+1}')
        plt.xlabel('Predicted')
        plt.ylabel('Actual')

        cm_path = Path(self.config['paths']['output_dir']) / f'confusion_matrix_epoch_{epoch+1}.png'
        plt.savefig(cm_path, dpi=150, bbox_inches='tight')
        plt.close()

        return cm

    def unfreeze_encoder(self):
        """Unfreeze the encoder for fine-tuning"""
        print("🔓 Unfreezing 3D CNN encoder for fine-tuning...")
        for param in self.model.cnn_backbone.parameters():
            param.requires_grad = True

        # Update optimizer with different learning rates
        encoder_param_ids = set(id(p) for p in self.model.cnn_backbone.parameters())
        encoder_params = [p for p in self.model.parameters() if id(p) in encoder_param_ids]
        head_params = [p for p in self.model.parameters() if id(p) not in encoder_param_ids]

        self.optimizer = optim.AdamW([
            {'params': encoder_params, 'lr': self.config['finetune']['lr_encoder']},
            {'params': head_params, 'lr': self.config['finetune']['lr_head']}
        ], weight_decay=self.config['optimizer']['weight_decay'])

        print(f"   Encoder LR: {self.config['finetune']['lr_encoder']}")
        print(f"   Head LR: {self.config['finetune']['lr_head']}")

    def train(self):
        """Main training loop"""
        print("🚀 Starting LipNet 5-word training...")
        print("=" * 60)

        target_accuracy = self.config['training']['target_accuracy'] * 100  # Convert to percentage

        for epoch in range(self.config['training']['epochs']):
            print(f"\n📅 Epoch {epoch+1}/{self.config['training']['epochs']}")
            print("-" * 40)

            # Check if we should unfreeze encoder
            if (epoch == self.config['finetune']['unfreeze_at_epoch'] and
                self.config['model']['freeze_encoder'] and
                self.best_accuracy < target_accuracy):
                self.unfreeze_encoder()

            # Training
            start_time = time.time()
            train_loss, train_acc = self.train_epoch(epoch)

            # Validation
            val_loss, val_acc, predictions, labels = self.validate()

            epoch_time = time.time() - start_time

            # Update training history
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_acc'].append(train_acc)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_acc'].append(val_acc)

            # Print epoch results
            print(f"\n📊 Epoch {epoch+1} Results:")
            print(f"   Train Loss: {train_loss:.4f} | Train Acc: {train_acc:.2f}%")
            print(f"   Val Loss: {val_loss:.4f} | Val Acc: {val_acc:.2f}%")
            print(f"   Time: {epoch_time:.1f}s")

            # Check if this is the best model
            is_best = val_acc > self.best_accuracy
            if is_best:
                self.best_accuracy = val_acc
                print(f"🎉 New best validation accuracy: {val_acc:.2f}%")

            # Save checkpoint
            self.save_checkpoint(epoch, val_acc, is_best)

            # Plot confusion matrix for best epochs
            if is_best or epoch == self.config['training']['epochs'] - 1:
                cm = self.plot_confusion_matrix(predictions, labels, epoch)

                # Print classification report
                phrases = self.config['phrases']
                report = classification_report(labels, predictions, target_names=phrases)
                print(f"\n📋 Classification Report (Epoch {epoch+1}):")
                print(report)

            # Early stopping check
            if val_acc >= target_accuracy:
                print(f"\n🎯 Target accuracy {target_accuracy:.1f}% achieved!")
                print(f"   Final validation accuracy: {val_acc:.2f}%")
                break

            # Early stopping patience
            if epoch >= self.config['training']['early_stopping_patience']:
                recent_accs = self.training_history['val_acc'][-self.config['training']['early_stopping_patience']:]
                if max(recent_accs) - min(recent_accs) < self.config['training']['min_delta']:
                    print(f"\n⏹️  Early stopping: No improvement in {self.config['training']['early_stopping_patience']} epochs")
                    break

        # Final results
        print(f"\n✅ Training completed!")
        print(f"   Best validation accuracy: {self.best_accuracy:.2f}%")
        print(f"   Target achieved: {'Yes' if self.best_accuracy >= target_accuracy else 'No'}")

        # Plot training history
        self.plot_training_history()

        # Save final training summary
        summary = {
            'best_accuracy': self.best_accuracy,
            'target_accuracy': target_accuracy,
            'target_achieved': self.best_accuracy >= target_accuracy,
            'total_epochs': len(self.training_history['val_acc']),
            'final_train_acc': self.training_history['train_acc'][-1],
            'final_val_acc': self.training_history['val_acc'][-1],
            'config': self.config
        }

        summary_path = Path(self.config['paths']['output_dir']) / 'training_summary.json'
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)

        print(f"📄 Training summary saved: {summary_path}")

        return self.best_accuracy >= target_accuracy

def main():
    """Main function"""
    config_path = "configs/core_words_5.yaml"

    if not Path(config_path).exists():
        print(f"❌ Configuration file not found: {config_path}")
        return False

    # Create trainer and start training
    trainer = LipNet5WordTrainer(config_path)
    success = trainer.train()

    if success:
        print(f"\n🎉 SUCCESS: >80% validation accuracy achieved!")
        print(f"📁 Model saved to: {trainer.config['paths']['best_model_path']}")
    else:
        print(f"\n⚠️  Target accuracy not reached. Best: {trainer.best_accuracy:.2f}%")
        print(f"💡 Consider: More epochs, data augmentation, or architecture changes")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
