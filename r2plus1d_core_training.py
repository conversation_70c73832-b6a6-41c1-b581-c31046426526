#!/usr/bin/env python3
"""
Core R(2+1)D-18 transfer learning for >80% validation accuracy.
Focus on proven architecture: R(2+1)D-18 (Kinetics-400) + BiGRU + Linear classifier
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.models.video as video_models
import torchvision.transforms as transforms
import pandas as pd
import numpy as np
from pathlib import Path
import json
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, classification_report
import warnings
warnings.filterwarnings('ignore')

class R2Plus1DDataset(Dataset):
    """Dataset for R(2+1)D model with 16-frame clips"""
    
    def __init__(self, manifest_path, target_frames=16):
        self.manifest = pd.read_csv(manifest_path)
        self.target_frames = target_frames
        
        # Verify all files exist
        valid_rows = []
        for idx, row in self.manifest.iterrows():
            if Path(row['video_path']).exists():
                valid_rows.append(row)
        
        self.manifest = pd.DataFrame(valid_rows).reset_index(drop=True)
        
        print(f"📊 R(2+1)D Dataset loaded: {len(self.manifest)} videos")
        class_counts = self.manifest['word'].value_counts()
        print(f"   Class distribution: {dict(class_counts)}")
        
        # Kinetics-400 normalization stats
        self.normalize = transforms.Normalize(
            mean=[0.43216, 0.394666, 0.37645],
            std=[0.22803, 0.22145, 0.216989]
        )
    
    def __len__(self):
        return len(self.manifest)
    
    def __getitem__(self, idx):
        row = self.manifest.iloc[idx]
        
        # Load preprocessed tensor
        video_tensor = torch.load(row['video_path'])  # (1, T, H, W)
        
        # Ensure correct shape
        if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
            video_tensor = video_tensor.squeeze(0)  # (T, H, W)
        elif video_tensor.dim() == 3:
            pass  # Already (T, H, W)
        else:
            raise ValueError(f"Unexpected tensor shape: {video_tensor.shape}")
        
        # Sample exactly 16 frames
        T, H, W = video_tensor.shape
        if T >= self.target_frames:
            # Sample evenly
            indices = torch.linspace(0, T - 1, self.target_frames).long()
            video_tensor = video_tensor[indices]
        else:
            # Repeat frames to reach target
            repeat_factor = (self.target_frames + T - 1) // T
            video_tensor = video_tensor.repeat(repeat_factor, 1, 1)[:self.target_frames]
        
        # Convert grayscale to RGB: (T, H, W) -> (T, 3, H, W)
        video_tensor = video_tensor.unsqueeze(1).repeat(1, 3, 1, 1)
        
        # Normalize from [-1, 1] to [0, 1] first
        video_tensor = (video_tensor + 1.0) / 2.0
        
        # Apply Kinetics-400 normalization
        for t in range(self.target_frames):
            video_tensor[t] = self.normalize(video_tensor[t])
        
        # Rearrange to (3, T, H, W) for R(2+1)D
        video_tensor = video_tensor.permute(1, 0, 2, 3)
        
        label = torch.tensor(row['label'], dtype=torch.long)
        
        return video_tensor, label

class R2Plus1DLipNet(nn.Module):
    """R(2+1)D-18 + BiGRU classifier for lipreading"""
    
    def __init__(self, num_classes=5, hidden_size=256):
        super(R2Plus1DLipNet, self).__init__()
        
        # Load pretrained R(2+1)D-18
        try:
            # Try new API first
            self.backbone = video_models.r2plus1d_18(weights='KINETICS400_V1')
        except TypeError:
            # Fallback to older API
            self.backbone = video_models.r2plus1d_18(pretrained=True)
        
        # Remove final classifier to get 512-dim features
        self.backbone.fc = nn.Identity()
        
        # BiGRU for temporal modeling
        self.bigru = nn.GRU(
            input_size=512,
            hidden_size=hidden_size,
            num_layers=1,
            batch_first=True,
            bidirectional=True
        )
        
        # Final classifier
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(hidden_size * 2, num_classes)  # *2 for bidirectional
        )
        
        print(f"🤖 R(2+1)D-18 + BiGRU model created")
        print(f"   Backbone: Kinetics-400 pretrained R(2+1)D-18")
        print(f"   BiGRU hidden size: {hidden_size}")
        print(f"   Output classes: {num_classes}")
    
    def forward(self, x):
        # x: (batch, 3, 16, 112, 112)
        batch_size = x.size(0)
        
        # Extract features with R(2+1)D backbone
        features = self.backbone(x)  # (batch, 512)
        
        # For BiGRU, we need sequence dimension
        # Since R(2+1)D processes the entire clip, we'll use the feature as a single timestep
        features = features.unsqueeze(1)  # (batch, 1, 512)
        
        # BiGRU processing
        gru_out, _ = self.bigru(features)  # (batch, 1, 512)
        
        # Take the output from the single timestep
        gru_out = gru_out.squeeze(1)  # (batch, 512)
        
        # Classification
        output = self.classifier(gru_out)
        
        return output

class R2Plus1DTrainer:
    """Two-phase trainer: frozen backbone -> light fine-tuning"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  Using device: {self.device}")
        
        self.batch_size = 8
        self.target_accuracy = 80.0
        
        # Create output directory
        self.output_dir = Path("artifacts/vsr_fasthead_v1")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🎯 R(2+1)D Transfer Learning Pipeline:")
        print(f"   Target accuracy: {self.target_accuracy}%")
        print(f"   Batch size: {self.batch_size}")
        print(f"   Two-phase training: Frozen → Fine-tuning")
    
    def create_data_loaders(self):
        """Create data loaders for training and validation"""
        
        # Training dataset
        train_dataset = R2Plus1DDataset(
            "data/speaker_separated_processed/train_processed_manifest.csv",
            target_frames=16
        )
        
        # Validation dataset
        val_dataset = R2Plus1DDataset(
            "data/speaker_separated_processed/val_processed_manifest.csv",
            target_frames=16
        )
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=2,
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=2,
            pin_memory=True
        )
        
        return train_loader, val_loader
    
    def phase1_frozen_training(self, model, train_loader, val_loader):
        """Phase 1: Train only BiGRU + classifier with frozen R(2+1)D backbone"""
        print("\n🔒 Phase 1: Frozen Backbone Training")
        print("=" * 50)
        
        # Freeze all backbone parameters
        for param in model.backbone.parameters():
            param.requires_grad = False
        
        # Only train BiGRU and classifier
        trainable_params = list(model.bigru.parameters()) + list(model.classifier.parameters())
        
        optimizer = optim.Adam(trainable_params, lr=1e-3, weight_decay=0.01)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='max', factor=0.5, patience=5, verbose=True
        )
        criterion = nn.CrossEntropyLoss()
        
        best_val_acc = 0.0
        epochs = 30  # Increased epochs
        patience = 15  # Increased patience
        epochs_without_improvement = 0
        
        for epoch in range(epochs):
            print(f"\n📅 Phase 1 Epoch {epoch+1}/{epochs}")
            
            # Training
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for videos, labels in tqdm(train_loader, desc="Training"):
                videos, labels = videos.to(self.device), labels.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(videos)
                loss = criterion(outputs, labels)
                loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(trainable_params, 1.0)
                
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = outputs.max(1)
                train_total += labels.size(0)
                train_correct += predicted.eq(labels).sum().item()
            
            # Validation
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            all_predictions = []
            all_labels = []
            
            with torch.no_grad():
                for videos, labels in tqdm(val_loader, desc="Validation"):
                    videos, labels = videos.to(self.device), labels.to(self.device)
                    
                    outputs = model(videos)
                    loss = criterion(outputs, labels)
                    
                    val_loss += loss.item()
                    _, predicted = outputs.max(1)
                    val_total += labels.size(0)
                    val_correct += predicted.eq(labels).sum().item()
                    
                    all_predictions.extend(predicted.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())
            
            train_acc = 100. * train_correct / train_total
            val_acc = 100. * val_correct / val_total
            
            print(f"📊 Results:")
            print(f"   Train Loss: {train_loss/len(train_loader):.4f} | Train Acc: {train_acc:.2f}%")
            print(f"   Val Loss: {val_loss/len(val_loader):.4f} | Val Acc: {val_acc:.2f}%")
            
            scheduler.step(val_acc)
            
            # Check improvement
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                epochs_without_improvement = 0
                
                # Save best model
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'accuracy': val_acc,
                    'phase': 1,
                    'training_config': {
                        'batch_size': self.batch_size,
                        'learning_rate': 1e-3,
                        'architecture': 'R2Plus1D_BiGRU'
                    }
                }, self.output_dir / 'r2plus1d_phase1_best.pth')
                
                print(f"🎉 NEW BEST: {val_acc:.2f}% validation accuracy!")
                
                # Print classification report
                words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
                report = classification_report(all_labels, all_predictions, target_names=words, digits=3)
                print(f"\n📋 Classification Report:")
                print(report)
                
                # Check if target achieved
                if val_acc >= self.target_accuracy:
                    print(f"\n🎯 TARGET ACHIEVED: {self.target_accuracy:.1f}% validation accuracy!")
                    return True, best_val_acc
                
            else:
                epochs_without_improvement += 1
            
            # Early stopping
            if epochs_without_improvement >= patience:
                print(f"\n⏹️  Early stopping: No improvement for {patience} epochs")
                break
        
        print(f"\n✅ Phase 1 completed! Best validation accuracy: {best_val_acc:.2f}%")
        return best_val_acc >= self.target_accuracy, best_val_acc

    def phase2_fine_tuning(self, model, train_loader, val_loader):
        """Phase 2: Fine-tune last layers of R(2+1)D backbone"""
        print("\n🔓 Phase 2: Fine-tuning Last Layers")
        print("=" * 50)

        # Load best Phase 1 model
        checkpoint_path = self.output_dir / 'r2plus1d_phase1_best.pth'
        if checkpoint_path.exists():
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            model.load_state_dict(checkpoint['model_state_dict'])
            print("✅ Loaded best Phase 1 model")

        # Unfreeze only the last layer (layer4) of the backbone
        for param in model.backbone.parameters():
            param.requires_grad = False

        # Unfreeze layer4 (final residual block)
        if hasattr(model.backbone, 'layer4'):
            for param in model.backbone.layer4.parameters():
                param.requires_grad = True
            print("🔓 Unfroze backbone.layer4 for fine-tuning")

        # Get trainable parameters
        trainable_params = []
        for name, param in model.named_parameters():
            if param.requires_grad:
                trainable_params.append(param)

        print(f"📊 Trainable parameters: {sum(p.numel() for p in trainable_params):,}")

        # Lower learning rate for fine-tuning
        optimizer = optim.Adam(trainable_params, lr=1e-4, weight_decay=0.01)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='max', factor=0.5, patience=8, verbose=True
        )
        criterion = nn.CrossEntropyLoss()

        best_val_acc = 0.0
        epochs = 25
        patience = 12
        epochs_without_improvement = 0

        for epoch in range(epochs):
            print(f"\n📅 Phase 2 Epoch {epoch+1}/{epochs}")

            # Training
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0

            for videos, labels in tqdm(train_loader, desc="Fine-tuning"):
                videos, labels = videos.to(self.device), labels.to(self.device)

                optimizer.zero_grad()
                outputs = model(videos)
                loss = criterion(outputs, labels)
                loss.backward()

                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(trainable_params, 0.5)

                optimizer.step()

                train_loss += loss.item()
                _, predicted = outputs.max(1)
                train_total += labels.size(0)
                train_correct += predicted.eq(labels).sum().item()

            # Validation
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            all_predictions = []
            all_labels = []

            with torch.no_grad():
                for videos, labels in tqdm(val_loader, desc="Validation"):
                    videos, labels = videos.to(self.device), labels.to(self.device)

                    outputs = model(videos)
                    loss = criterion(outputs, labels)

                    val_loss += loss.item()
                    _, predicted = outputs.max(1)
                    val_total += labels.size(0)
                    val_correct += predicted.eq(labels).sum().item()

                    all_predictions.extend(predicted.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())

            train_acc = 100. * train_correct / train_total
            val_acc = 100. * val_correct / val_total

            print(f"📊 Results:")
            print(f"   Train Loss: {train_loss/len(train_loader):.4f} | Train Acc: {train_acc:.2f}%")
            print(f"   Val Loss: {val_loss/len(val_loader):.4f} | Val Acc: {val_acc:.2f}%")

            scheduler.step(val_acc)

            # Check improvement
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                epochs_without_improvement = 0

                # Save best model
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'accuracy': val_acc,
                    'phase': 2,
                    'training_config': {
                        'batch_size': self.batch_size,
                        'learning_rate': 1e-4,
                        'architecture': 'R2Plus1D_BiGRU_FineTuned'
                    }
                }, self.output_dir / 'r2plus1d_phase2_best.pth')

                print(f"🎉 NEW BEST: {val_acc:.2f}% validation accuracy!")

                # Print classification report
                words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
                report = classification_report(all_labels, all_predictions, target_names=words, digits=3)
                print(f"\n📋 Classification Report:")
                print(report)

                # Check if target achieved
                if val_acc >= self.target_accuracy:
                    print(f"\n🎯 TARGET ACHIEVED: {self.target_accuracy:.1f}% validation accuracy!")
                    return True, best_val_acc

            else:
                epochs_without_improvement += 1

            # Early stopping
            if epochs_without_improvement >= patience:
                print(f"\n⏹️  Early stopping: No improvement for {patience} epochs")
                break

        print(f"\n✅ Phase 2 completed! Best validation accuracy: {best_val_acc:.2f}%")
        return best_val_acc >= self.target_accuracy, best_val_acc

def main():
    """Main function for R(2+1)D training"""
    print("🚀 R(2+1)D-18 Transfer Learning for >80% Validation Accuracy")
    print("=" * 70)
    
    # Create trainer
    trainer = R2Plus1DTrainer()
    
    # Create data loaders
    train_loader, val_loader = trainer.create_data_loaders()
    
    # Create model
    model = R2Plus1DLipNet(num_classes=5, hidden_size=256)
    model.to(trainer.device)
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"📊 Model Statistics:")
    print(f"   Total parameters: {total_params:,}")
    print(f"   Trainable parameters: {trainable_params:,}")
    
    # Phase 1: Frozen backbone training
    phase1_success, phase1_acc = trainer.phase1_frozen_training(model, train_loader, val_loader)
    
    if phase1_success:
        print(f"\n🎉 SUCCESS: Phase 1 achieved {phase1_acc:.2f}% > {trainer.target_accuracy}%!")
        print(f"📁 Model saved: {trainer.output_dir}/r2plus1d_phase1_best.pth")
        return True
    else:
        print(f"\n📊 Phase 1 result: {phase1_acc:.2f}% (target: {trainer.target_accuracy}%)")
        if phase1_acc >= 30.0:  # Lower threshold to try Phase 2
            print("🔄 Phase 1 shows promise, implementing Phase 2 fine-tuning...")

            # Phase 2: Fine-tuning
            phase2_success, phase2_acc = trainer.phase2_fine_tuning(model, train_loader, val_loader)

            if phase2_success:
                print(f"\n🎉 SUCCESS: Phase 2 achieved {phase2_acc:.2f}% > {trainer.target_accuracy}%!")
                print(f"📁 Model saved: {trainer.output_dir}/r2plus1d_phase2_best.pth")
                return True
            else:
                print(f"\n📊 Phase 2 result: {phase2_acc:.2f}% (target: {trainer.target_accuracy}%)")
                print("💡 Consider expanding validation set or different approach")
                return False
        else:
            print("⚠️  Phase 1 accuracy too low for fine-tuning")
            return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
