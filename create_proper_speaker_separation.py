#!/usr/bin/env python3
"""
Create proper speaker-separated datasets from the original dataset.
Ensure no speaker appears in multiple splits and all words are represented.
"""

import os
import shutil
import pandas as pd
import numpy as np
from pathlib import Path
import json
from collections import defaultdict, Counter
import re

def extract_speaker_from_original_filename(filename):
    """
    Extract speaker ID from original dataset filenames.
    Look for patterns that might indicate different speakers.
    """
    # Remove extension
    name = Path(filename).stem
    
    # Extract the number part which likely represents different speakers/videos
    match = re.search(r'(\w+)\s+(\d+)', name)
    if match:
        word = match.group(1)
        number = int(match.group(2))
        return f"speaker_{number:02d}"
    
    return "unknown"

def analyze_original_dataset():
    """Analyze the original dataset to understand speaker distribution"""
    original_path = Path("data/cropped videos 1.9.25")
    
    if not original_path.exists():
        print(f"❌ Original dataset not found: {original_path}")
        return None
    
    print("🔍 Analyzing original dataset for proper speaker separation...")
    
    all_videos = []
    words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
    
    for word in words:
        word_path = original_path / word
        if word_path.exists():
            video_files = list(word_path.glob("*.mp4"))
            print(f"   {word}: {len(video_files)} videos")
            
            for video_file in video_files:
                speaker_id = extract_speaker_from_original_filename(video_file.name)
                all_videos.append({
                    'word': word,
                    'filename': video_file.name,
                    'path': str(video_file),
                    'speaker_id': speaker_id
                })
    
    # Group by speaker
    speaker_videos = defaultdict(list)
    for video in all_videos:
        speaker_videos[video['speaker_id']].append(video)
    
    print(f"\n📊 Speaker distribution in original dataset:")
    for speaker_id, videos in sorted(speaker_videos.items()):
        word_counts = Counter(v['word'] for v in videos)
        print(f"   {speaker_id}: {len(videos)} videos - {dict(word_counts)}")
    
    return all_videos, speaker_videos

def create_balanced_speaker_splits(speaker_videos, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15):
    """
    Create balanced speaker splits ensuring no speaker overlap.
    """
    print(f"\n🎯 Creating balanced speaker splits...")
    
    # Get all speakers
    all_speakers = list(speaker_videos.keys())
    all_speakers = [s for s in all_speakers if s != 'unknown']  # Remove unknown speakers
    
    print(f"   Total speakers: {len(all_speakers)}")
    
    # Shuffle speakers for random assignment
    np.random.seed(42)  # For reproducibility
    np.random.shuffle(all_speakers)
    
    # Calculate split sizes
    n_speakers = len(all_speakers)
    n_train = int(n_speakers * train_ratio)
    n_val = int(n_speakers * val_ratio)
    n_test = n_speakers - n_train - n_val
    
    # Assign speakers to splits
    train_speakers = all_speakers[:n_train]
    val_speakers = all_speakers[n_train:n_train + n_val]
    test_speakers = all_speakers[n_train + n_val:]
    
    print(f"   Training speakers: {len(train_speakers)}")
    print(f"   Validation speakers: {len(val_speakers)}")
    print(f"   Test speakers: {len(test_speakers)}")
    
    # Create video splits
    train_videos = []
    val_videos = []
    test_videos = []
    
    for speaker_id in train_speakers:
        train_videos.extend(speaker_videos[speaker_id])
    
    for speaker_id in val_speakers:
        val_videos.extend(speaker_videos[speaker_id])
    
    for speaker_id in test_speakers:
        test_videos.extend(speaker_videos[speaker_id])
    
    # Check word distribution in each split
    def check_word_distribution(videos, split_name):
        word_counts = Counter(v['word'] for v in videos)
        print(f"   {split_name}: {len(videos)} videos")
        for word in ['doctor', 'glasses', 'help', 'phone', 'pillow']:
            count = word_counts.get(word, 0)
            print(f"      {word}: {count}")
        return word_counts
    
    print(f"\n📊 Word distribution by split:")
    train_word_counts = check_word_distribution(train_videos, "Training")
    val_word_counts = check_word_distribution(val_videos, "Validation")
    test_word_counts = check_word_distribution(test_videos, "Test")
    
    # Check if all words are represented in training
    missing_words_train = [word for word in ['doctor', 'glasses', 'help', 'phone', 'pillow'] 
                          if train_word_counts.get(word, 0) == 0]
    
    if missing_words_train:
        print(f"   ⚠️  Missing words in training: {missing_words_train}")
        print(f"   🔧 Attempting to rebalance...")
        
        # Try to move some speakers to ensure all words are in training
        # This is a simple heuristic - in practice, you might need more sophisticated balancing
        for word in missing_words_train:
            # Find speakers in val/test that have this word
            for speaker_id in val_speakers + test_speakers:
                speaker_words = [v['word'] for v in speaker_videos[speaker_id]]
                if word in speaker_words:
                    # Move this speaker to training
                    if speaker_id in val_speakers:
                        val_speakers.remove(speaker_id)
                        val_videos = [v for v in val_videos if v['speaker_id'] != speaker_id]
                    else:
                        test_speakers.remove(speaker_id)
                        test_videos = [v for v in test_videos if v['speaker_id'] != speaker_id]
                    
                    train_speakers.append(speaker_id)
                    train_videos.extend(speaker_videos[speaker_id])
                    break
        
        print(f"   ✅ Rebalanced splits:")
        print(f"      Training speakers: {len(train_speakers)}")
        print(f"      Validation speakers: {len(val_speakers)}")
        print(f"      Test speakers: {len(test_speakers)}")
    
    return {
        'train': {'speakers': train_speakers, 'videos': train_videos},
        'val': {'speakers': val_speakers, 'videos': val_videos},
        'test': {'speakers': test_speakers, 'videos': test_videos}
    }

def copy_videos_to_splits(splits):
    """Copy videos to new split directories"""
    print(f"\n📁 Creating new speaker-separated dataset directories...")
    
    # Create output directories
    output_base = Path("data/speaker_separated_corrected")
    output_base.mkdir(exist_ok=True)
    
    for split_name in ['train', 'val', 'test']:
        split_dir = output_base / split_name
        split_dir.mkdir(exist_ok=True)
        
        videos = splits[split_name]['videos']
        print(f"   Copying {len(videos)} videos to {split_name}...")
        
        for video in videos:
            src_path = Path(video['path'])
            if src_path.exists():
                # Create filename with word prefix for clarity
                dst_filename = f"{video['word']}_{video['filename']}"
                dst_path = split_dir / dst_filename
                
                try:
                    shutil.copy2(src_path, dst_path)
                except Exception as e:
                    print(f"      ❌ Failed to copy {src_path}: {e}")
    
    print(f"   ✅ Videos copied to: {output_base}")
    return output_base

def create_manifests(splits, output_base):
    """Create manifest files for the corrected splits"""
    print(f"\n📝 Creating manifest files...")
    
    words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
    
    for split_name in ['train', 'val', 'test']:
        videos = splits[split_name]['videos']
        manifest_data = []
        
        for video in videos:
            # Update path to point to new location
            new_filename = f"{video['word']}_{video['filename']}"
            new_path = output_base / split_name / new_filename
            
            manifest_data.append({
                'video_path': str(new_path),
                'word': video['word'],
                'label': words.index(video['word']),
                'split': split_name,
                'speaker': video['speaker_id'],
                'original_filename': video['filename']
            })
        
        # Save manifest
        manifest_df = pd.DataFrame(manifest_data)
        manifest_path = output_base / f"{split_name}_manifest.csv"
        manifest_df.to_csv(manifest_path, index=False)
        
        print(f"   ✅ {split_name} manifest: {manifest_path} ({len(manifest_df)} videos)")
    
    # Combined manifest
    all_manifests = []
    for split_name in ['train', 'val', 'test']:
        videos = splits[split_name]['videos']
        for video in videos:
            new_filename = f"{video['word']}_{video['filename']}"
            new_path = output_base / split_name / new_filename
            
            all_manifests.append({
                'video_path': str(new_path),
                'word': video['word'],
                'label': words.index(video['word']),
                'split': split_name,
                'speaker': video['speaker_id'],
                'original_filename': video['filename']
            })
    
    combined_df = pd.DataFrame(all_manifests)
    combined_path = output_base / "combined_manifest.csv"
    combined_df.to_csv(combined_path, index=False)
    print(f"   ✅ Combined manifest: {combined_path} ({len(combined_df)} videos)")
    
    return output_base

def verify_separation(splits):
    """Verify that speaker separation is perfect"""
    print(f"\n✅ Verifying speaker separation...")
    
    train_speakers = set(splits['train']['speakers'])
    val_speakers = set(splits['val']['speakers'])
    test_speakers = set(splits['test']['speakers'])
    
    overlaps = []
    if train_speakers & val_speakers:
        overlaps.append(f"Train-Val: {train_speakers & val_speakers}")
    if train_speakers & test_speakers:
        overlaps.append(f"Train-Test: {train_speakers & test_speakers}")
    if val_speakers & test_speakers:
        overlaps.append(f"Val-Test: {val_speakers & test_speakers}")
    
    if overlaps:
        print(f"   ❌ Speaker overlaps found:")
        for overlap in overlaps:
            print(f"      {overlap}")
        return False
    else:
        print(f"   ✅ Perfect speaker separation achieved!")
        return True

def main():
    """Main function to create proper speaker separation"""
    print("🚀 Creating Proper Speaker-Separated Dataset")
    print("=" * 60)
    
    # Analyze original dataset
    all_videos, speaker_videos = analyze_original_dataset()
    
    if not all_videos:
        print("❌ Failed to analyze original dataset")
        return False
    
    # Create balanced splits
    splits = create_balanced_speaker_splits(speaker_videos)
    
    # Verify separation
    separation_ok = verify_separation(splits)
    
    if not separation_ok:
        print("❌ Failed to achieve perfect speaker separation")
        return False
    
    # Copy videos to new directories
    output_base = copy_videos_to_splits(splits)
    
    # Create manifests
    manifest_dir = create_manifests(splits, output_base)
    
    # Save split information
    split_info = {
        'train_speakers': splits['train']['speakers'],
        'val_speakers': splits['val']['speakers'],
        'test_speakers': splits['test']['speakers'],
        'train_videos': len(splits['train']['videos']),
        'val_videos': len(splits['val']['videos']),
        'test_videos': len(splits['test']['videos']),
        'output_directory': str(output_base),
        'perfect_separation': separation_ok
    }
    
    info_path = output_base / "split_info.json"
    with open(info_path, 'w') as f:
        json.dump(split_info, f, indent=2)
    
    print(f"\n🎉 Success! Proper speaker-separated dataset created:")
    print(f"   📁 Dataset directory: {output_base}")
    print(f"   📊 Training: {len(splits['train']['videos'])} videos, {len(splits['train']['speakers'])} speakers")
    print(f"   📊 Validation: {len(splits['val']['videos'])} videos, {len(splits['val']['speakers'])} speakers")
    print(f"   📊 Test: {len(splits['test']['videos'])} videos, {len(splits['test']['speakers'])} speakers")
    print(f"   ✅ Perfect speaker separation: {separation_ok}")
    
    return True

if __name__ == "__main__":
    success = main()
