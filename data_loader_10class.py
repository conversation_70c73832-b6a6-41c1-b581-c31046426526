#!/usr/bin/env python3
"""
Data Loader and Augmentation Pipeline for 10-Class Lipreading
============================================================

Creates data loader for preprocessed .pt files with class-balanced sampling and
implements data augmentation while preserving lip-reading features:

- Temporal jitter ±2-4 frames
- Spatial RandomResizedCrop 0.9-1.0
- Horizontal flip p=0.4
- Photometric brightness/contrast ±15%
- Gaussian noise σ=0.02

Usage:
    from data_loader_10class import create_10class_dataloaders
    train_loader, val_loader, test_loader = create_10class_dataloaders(
        processed_dir="data/10class_processed",
        batch_size=16
    )
"""

import os
import json
import torch
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import random
from collections import Counter
import logging

logger = logging.getLogger(__name__)

class VideoAugmentation:
    """Video augmentation transforms for lipreading"""
    
    def __init__(self, 
                 temporal_jitter: int = 3,
                 spatial_crop_scale: Tuple[float, float] = (0.9, 1.0),
                 horizontal_flip_prob: float = 0.4,
                 brightness_contrast_range: float = 0.15,
                 gaussian_noise_std: float = 0.02):
        """
        Initialize video augmentation
        
        Args:
            temporal_jitter: Maximum frames to jitter (±temporal_jitter)
            spatial_crop_scale: Scale range for random resized crop
            horizontal_flip_prob: Probability of horizontal flip
            brightness_contrast_range: Range for brightness/contrast adjustment (±range)
            gaussian_noise_std: Standard deviation for Gaussian noise
        """
        self.temporal_jitter = temporal_jitter
        self.spatial_crop_scale = spatial_crop_scale
        self.horizontal_flip_prob = horizontal_flip_prob
        self.brightness_contrast_range = brightness_contrast_range
        self.gaussian_noise_std = gaussian_noise_std
    
    def temporal_jitter_augment(self, video: torch.Tensor) -> torch.Tensor:
        """
        Apply temporal jitter by randomly shifting frame selection
        
        Args:
            video: Video tensor of shape (T, C, H, W)
            
        Returns:
            Jittered video tensor
        """
        T, C, H, W = video.shape
        
        if T <= 16:
            # If video is too short, just return as is
            return video
        
        # Random jitter within bounds
        max_jitter = min(self.temporal_jitter, (T - 16) // 2)
        if max_jitter > 0:
            jitter = random.randint(-max_jitter, max_jitter)
            start_idx = max(0, 8 + jitter)  # Center around middle
            end_idx = start_idx + 16
            
            if end_idx > T:
                end_idx = T
                start_idx = T - 16
            
            video = video[start_idx:end_idx]
        
        return video
    
    def spatial_crop_augment(self, video: torch.Tensor) -> torch.Tensor:
        """
        Apply random resized crop
        
        Args:
            video: Video tensor of shape (T, C, H, W)
            
        Returns:
            Cropped and resized video tensor
        """
        T, C, H, W = video.shape
        
        # Random scale
        scale = random.uniform(*self.spatial_crop_scale)
        
        # Calculate crop size
        crop_h = int(H * scale)
        crop_w = int(W * scale)
        
        # Random crop position
        top = random.randint(0, H - crop_h)
        left = random.randint(0, W - crop_w)
        
        # Crop
        video = video[:, :, top:top+crop_h, left:left+crop_w]
        
        # Resize back to original size
        video = F.interpolate(
            video.view(T*C, 1, crop_h, crop_w),
            size=(H, W),
            mode='bilinear',
            align_corners=False
        ).view(T, C, H, W)
        
        return video
    
    def horizontal_flip_augment(self, video: torch.Tensor) -> torch.Tensor:
        """
        Apply horizontal flip
        
        Args:
            video: Video tensor of shape (T, C, H, W)
            
        Returns:
            Flipped video tensor
        """
        if random.random() < self.horizontal_flip_prob:
            video = torch.flip(video, dims=[3])  # Flip width dimension
        
        return video
    
    def photometric_augment(self, video: torch.Tensor) -> torch.Tensor:
        """
        Apply brightness and contrast augmentation
        
        Args:
            video: Video tensor of shape (T, C, H, W)
            
        Returns:
            Augmented video tensor
        """
        # Random brightness adjustment
        brightness_factor = 1.0 + random.uniform(-self.brightness_contrast_range, 
                                                 self.brightness_contrast_range)
        video = video * brightness_factor
        
        # Random contrast adjustment
        contrast_factor = 1.0 + random.uniform(-self.brightness_contrast_range, 
                                              self.brightness_contrast_range)
        mean = video.mean(dim=(2, 3), keepdim=True)
        video = (video - mean) * contrast_factor + mean
        
        # Clamp to valid range
        video = torch.clamp(video, -1.0, 1.0)
        
        return video
    
    def gaussian_noise_augment(self, video: torch.Tensor) -> torch.Tensor:
        """
        Add Gaussian noise
        
        Args:
            video: Video tensor of shape (T, C, H, W)
            
        Returns:
            Noisy video tensor
        """
        noise = torch.randn_like(video) * self.gaussian_noise_std
        video = video + noise
        
        # Clamp to valid range
        video = torch.clamp(video, -1.0, 1.0)
        
        return video
    
    def __call__(self, video: torch.Tensor) -> torch.Tensor:
        """
        Apply all augmentations
        
        Args:
            video: Video tensor of shape (T, C, H, W)
            
        Returns:
            Augmented video tensor
        """
        # Temporal augmentation
        video = self.temporal_jitter_augment(video)
        
        # Spatial augmentation
        video = self.spatial_crop_augment(video)
        video = self.horizontal_flip_augment(video)
        
        # Photometric augmentation
        video = self.photometric_augment(video)
        video = self.gaussian_noise_augment(video)
        
        return video

class TenClassDataset(Dataset):
    """Dataset for 10-class lipreading with preprocessed .pt files"""
    
    def __init__(self, 
                 data_dir: str,
                 split: str,
                 manifest_data: List[Dict],
                 class_to_idx: Dict[str, int],
                 transform: Optional[VideoAugmentation] = None):
        """
        Initialize dataset
        
        Args:
            data_dir: Directory containing preprocessed .pt files
            split: Split name ('train', 'val', 'test')
            manifest_data: List of video metadata dictionaries
            class_to_idx: Mapping from class names to indices
            transform: Video augmentation transform
        """
        self.data_dir = Path(data_dir)
        self.split = split
        self.manifest_data = manifest_data
        self.class_to_idx = class_to_idx
        self.transform = transform
        
        # Filter data for this split
        self.split_data = [item for item in manifest_data if item.get('split') == split]
        
        logger.info(f"📊 {split.upper()} dataset: {len(self.split_data)} videos")
        
        # Count classes
        class_counts = Counter(item['class'] for item in self.split_data)
        for class_name, count in sorted(class_counts.items()):
            logger.info(f"   {class_name}: {count} videos")
    
    def __len__(self) -> int:
        return len(self.split_data)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int, Dict]:
        """
        Get item by index
        
        Args:
            idx: Item index
            
        Returns:
            Tuple of (video_tensor, label, metadata)
        """
        item = self.split_data[idx]
        
        # Load video tensor
        video_path = item['output_path']
        try:
            video_tensor = torch.load(video_path, map_location='cpu')
        except Exception as e:
            logger.error(f"Error loading {video_path}: {e}")
            # Return zero tensor as fallback
            video_tensor = torch.zeros(16, 1, 112, 112)
        
        # Get label
        class_name = item['class']
        label = self.class_to_idx[class_name]
        
        # Apply augmentation if provided
        if self.transform is not None:
            video_tensor = self.transform(video_tensor)
        
        # Ensure correct shape: (T, C, H, W) -> (C, T, H, W)
        if video_tensor.dim() == 4:
            video_tensor = video_tensor.permute(1, 0, 2, 3)  # (T,C,H,W) -> (C,T,H,W)
        
        # Create metadata
        metadata = {
            'video_path': video_path,
            'class': class_name,
            'label': label,
            'demographic_key': item.get('demographic_key', 'unknown'),
            'split': self.split
        }
        
        return video_tensor, label, metadata

def create_class_balanced_sampler(dataset: TenClassDataset) -> WeightedRandomSampler:
    """
    Create a weighted random sampler for class-balanced training
    
    Args:
        dataset: Dataset instance
        
    Returns:
        WeightedRandomSampler instance
    """
    # Count samples per class
    class_counts = Counter()
    for item in dataset.split_data:
        class_counts[item['class']] += 1
    
    # Calculate weights (inverse frequency)
    total_samples = len(dataset)
    num_classes = len(class_counts)
    
    class_weights = {}
    for class_name, count in class_counts.items():
        class_weights[class_name] = total_samples / (num_classes * count)
    
    # Create sample weights
    sample_weights = []
    for item in dataset.split_data:
        sample_weights.append(class_weights[item['class']])
    
    sampler = WeightedRandomSampler(
        weights=sample_weights,
        num_samples=len(sample_weights),
        replacement=True
    )
    
    logger.info(f"📊 Class-balanced sampler created:")
    for class_name, weight in sorted(class_weights.items()):
        logger.info(f"   {class_name}: weight={weight:.3f}")
    
    return sampler

def create_10class_dataloaders(processed_dir: str,
                              batch_size: int = 16,
                              num_workers: int = 4,
                              use_class_balancing: bool = True) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    Create data loaders for 10-class lipreading
    
    Args:
        processed_dir: Directory containing processed data
        batch_size: Batch size
        num_workers: Number of worker processes
        use_class_balancing: Whether to use class-balanced sampling for training
        
    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    processed_path = Path(processed_dir)
    
    # Load manifest
    manifest_path = processed_path / "processing_manifest.json"
    with open(manifest_path, 'r') as f:
        manifest_data = json.load(f)
    
    # Create class to index mapping
    all_classes = set()
    for split_data in manifest_data.values():
        for item in split_data:
            all_classes.add(item['class'])
    
    class_to_idx = {class_name: idx for idx, class_name in enumerate(sorted(all_classes))}
    idx_to_class = {idx: class_name for class_name, idx in class_to_idx.items()}
    
    logger.info(f"📊 10-Class Dataset Information:")
    logger.info(f"   Classes: {len(class_to_idx)}")
    for class_name, idx in sorted(class_to_idx.items()):
        logger.info(f"   {idx}: {class_name}")
    
    # Create augmentation transforms
    train_transform = VideoAugmentation(
        temporal_jitter=3,
        spatial_crop_scale=(0.9, 1.0),
        horizontal_flip_prob=0.4,
        brightness_contrast_range=0.15,
        gaussian_noise_std=0.02
    )
    
    # No augmentation for validation and test
    val_test_transform = None
    
    # Flatten manifest data
    all_data = []
    for split, split_data in manifest_data.items():
        for item in split_data:
            item['split'] = split
            all_data.append(item)
    
    # Create datasets
    train_dataset = TenClassDataset(
        processed_dir, 'train', all_data, class_to_idx, train_transform
    )
    val_dataset = TenClassDataset(
        processed_dir, 'val', all_data, class_to_idx, val_test_transform
    )
    test_dataset = TenClassDataset(
        processed_dir, 'test', all_data, class_to_idx, val_test_transform
    )
    
    # Create samplers
    train_sampler = None
    if use_class_balancing:
        train_sampler = create_class_balanced_sampler(train_dataset)
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        sampler=train_sampler,
        shuffle=(train_sampler is None),
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=False
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=False
    )
    
    logger.info(f"📊 Data Loaders Created:")
    logger.info(f"   Train batches: {len(train_loader)}")
    logger.info(f"   Val batches: {len(val_loader)}")
    logger.info(f"   Test batches: {len(test_loader)}")
    
    return train_loader, val_loader, test_loader, class_to_idx, idx_to_class

def test_data_loader():
    """Test the data loader"""
    print("🧪 Testing 10-class data loader...")
    
    try:
        train_loader, val_loader, test_loader, class_to_idx, idx_to_class = create_10class_dataloaders(
            processed_dir="data/10class_processed",
            batch_size=4,
            num_workers=0  # Use 0 for testing
        )
        
        # Test train loader
        print(f"\n🔍 Testing train loader...")
        for batch_idx, (videos, labels, metadata) in enumerate(train_loader):
            print(f"   Batch {batch_idx}: videos={videos.shape}, labels={labels.shape}")
            print(f"   Video range: [{videos.min():.3f}, {videos.max():.3f}]")
            print(f"   Labels: {labels.tolist()}")
            print(f"   Classes: {[idx_to_class[idx.item()] for idx in labels]}")
            
            if batch_idx >= 2:  # Test first 3 batches
                break
        
        print(f"✅ Data loader test completed successfully!")
        
    except Exception as e:
        print(f"❌ Data loader test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_loader()
