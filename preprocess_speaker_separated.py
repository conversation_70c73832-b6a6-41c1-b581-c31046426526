#!/usr/bin/env python3
"""
Preprocess speaker-separated datasets for LipNet training.
Convert videos to LipNet specifications: 64 frames, 112x112, grayscale, MediaPipe lip extraction.
"""

import os
import cv2
import numpy as np
import pandas as pd
import torch
from pathlib import Path
import json
from tqdm import tqdm
import mediapipe as mp
import time

class SpeakerSeparatedPreprocessor:
    """Preprocessor for speaker-separated datasets"""
    
    def __init__(self, target_size=(112, 112), target_fps=25, num_frames=64):
        self.target_size = target_size
        self.target_fps = target_fps
        self.num_frames = num_frames
        
        # Initialize MediaPipe Face Detection and Face Mesh
        self.mp_face_detection = mp.solutions.face_detection
        self.mp_face_mesh = mp.solutions.face_mesh
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Use Face Mesh for better lip region detection
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        
        # Lip landmark indices for MediaPipe Face Mesh
        self.lip_indices = [
            # Outer lip
            61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
            # Inner lip  
            78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415
        ]
        
        print(f"🎬 Preprocessor initialized:")
        print(f"   Target size: {target_size}")
        print(f"   Target frames: {num_frames}")
        print(f"   Target FPS: {target_fps}")
    
    def extract_lip_region_mediapipe(self, frame):
        """Extract lip region using MediaPipe Face Mesh"""
        try:
            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.face_mesh.process(rgb_frame)
            
            if results.multi_face_landmarks:
                face_landmarks = results.multi_face_landmarks[0]
                
                # Get image dimensions
                h, w = frame.shape[:2]
                
                # Extract lip landmarks
                lip_points = []
                for idx in self.lip_indices:
                    landmark = face_landmarks.landmark[idx]
                    x = int(landmark.x * w)
                    y = int(landmark.y * h)
                    lip_points.append((x, y))
                
                # Calculate bounding box around lip region
                if lip_points:
                    xs = [p[0] for p in lip_points]
                    ys = [p[1] for p in lip_points]
                    
                    min_x, max_x = min(xs), max(xs)
                    min_y, max_y = min(ys), max(ys)
                    
                    # Add padding around lip region
                    padding_x = int((max_x - min_x) * 0.3)
                    padding_y = int((max_y - min_y) * 0.3)
                    
                    min_x = max(0, min_x - padding_x)
                    max_x = min(w, max_x + padding_x)
                    min_y = max(0, min_y - padding_y)
                    max_y = min(h, max_y + padding_y)
                    
                    # Extract lip region
                    lip_region = frame[min_y:max_y, min_x:max_x]
                    
                    if lip_region.size > 0:
                        return lip_region
            
            # Fallback: use center crop if face detection fails
            return self._center_crop_fallback(frame)
            
        except Exception as e:
            print(f"   ⚠️  MediaPipe error: {e}")
            return self._center_crop_fallback(frame)
    
    def _center_crop_fallback(self, frame):
        """Fallback center crop when face detection fails"""
        h, w = frame.shape[:2]
        
        # Focus on lower center region (where mouth typically is)
        center_y = int(h * 0.65)  # Slightly below center
        center_x = w // 2
        
        # Crop size based on image dimensions
        crop_size = min(h, w) // 3
        
        y1 = max(0, center_y - crop_size // 2)
        y2 = min(h, center_y + crop_size // 2)
        x1 = max(0, center_x - crop_size // 2)
        x2 = min(w, center_x + crop_size // 2)
        
        return frame[y1:y2, x1:x2]
    
    def process_video(self, video_path, output_path):
        """Process a single video file"""
        try:
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                print(f"❌ Cannot open video: {video_path}")
                return False
            
            # Get video properties
            original_fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # Calculate frame sampling to get exactly num_frames
            if frame_count <= self.num_frames:
                # If video has fewer frames, repeat frames
                frame_indices = list(range(frame_count))
                while len(frame_indices) < self.num_frames:
                    frame_indices.extend(range(frame_count))
                frame_indices = frame_indices[:self.num_frames]
            else:
                # Sample frames evenly across the video
                frame_indices = np.linspace(0, frame_count - 1, self.num_frames, dtype=int)
            
            frames = []
            successful_extractions = 0
            
            for frame_idx in frame_indices:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                
                if not ret:
                    # Use last valid frame if available
                    if frames:
                        frame = frames[-1].copy()
                    else:
                        print(f"⚠️  Cannot read frame {frame_idx} from {video_path}")
                        continue
                
                # Extract lip region using MediaPipe
                lip_region = self.extract_lip_region_mediapipe(frame)
                
                if lip_region is not None and lip_region.size > 0:
                    successful_extractions += 1
                    
                    # Convert to grayscale
                    if len(lip_region.shape) == 3:
                        lip_region = cv2.cvtColor(lip_region, cv2.COLOR_BGR2GRAY)
                    
                    # Resize to target size
                    lip_region = cv2.resize(lip_region, self.target_size, interpolation=cv2.INTER_AREA)
                    
                    # Normalize to [0, 1]
                    lip_region = lip_region.astype(np.float32) / 255.0
                    
                    frames.append(lip_region)
                else:
                    # Use last valid frame if available
                    if frames:
                        frames.append(frames[-1].copy())
                    else:
                        # Create a blank frame
                        blank_frame = np.zeros(self.target_size, dtype=np.float32)
                        frames.append(blank_frame)
            
            cap.release()
            
            if len(frames) != self.num_frames:
                print(f"⚠️  Expected {self.num_frames} frames, got {len(frames)} for {video_path}")
                return False
            
            # Stack frames into tensor: (num_frames, height, width)
            video_tensor = np.stack(frames, axis=0)
            
            # Apply z-score normalization
            mean = np.mean(video_tensor)
            std = np.std(video_tensor)
            if std > 0:
                video_tensor = (video_tensor - mean) / std
            else:
                # If std is 0, just center the data
                video_tensor = video_tensor - mean
            
            # Convert to torch tensor and add channel dimension: (1, num_frames, height, width)
            video_tensor = torch.from_numpy(video_tensor).unsqueeze(0)
            
            # Save as .pt file
            output_path.parent.mkdir(parents=True, exist_ok=True)
            torch.save(video_tensor, output_path)
            
            # Return processing statistics
            return {
                'success': True,
                'successful_extractions': successful_extractions,
                'total_frames': self.num_frames,
                'extraction_rate': successful_extractions / self.num_frames
            }
            
        except Exception as e:
            print(f"❌ Error processing {video_path}: {e}")
            return {'success': False, 'error': str(e)}

def process_speaker_separated_datasets():
    """Process all speaker-separated datasets"""
    print("🎬 Processing Speaker-Separated Datasets for LipNet")
    print("=" * 70)
    
    # Input and output directories
    input_base = Path("data/speaker_separated_corrected")
    output_base = Path("data/speaker_separated_processed")
    
    if not input_base.exists():
        print(f"❌ Input directory not found: {input_base}")
        print("   Run fix_speaker_separation.py first!")
        return False
    
    # Create output directory
    output_base.mkdir(exist_ok=True)
    
    # Initialize preprocessor
    preprocessor = SpeakerSeparatedPreprocessor(
        target_size=(112, 112),
        target_fps=25,
        num_frames=64
    )
    
    # Process each split
    processing_results = {}
    
    for split_name in ['train', 'val', 'test']:
        print(f"\n📹 Processing {split_name} split...")
        
        # Load manifest
        manifest_path = input_base / f"{split_name}_manifest.csv"
        if not manifest_path.exists():
            print(f"   ❌ Manifest not found: {manifest_path}")
            continue
        
        manifest_df = pd.read_csv(manifest_path)
        print(f"   Found {len(manifest_df)} videos to process")
        
        # Create output directory for this split
        split_output_dir = output_base / split_name
        split_output_dir.mkdir(exist_ok=True)
        
        # Process videos
        processed_videos = []
        successful_count = 0
        total_extraction_rate = 0
        
        for idx, row in tqdm(manifest_df.iterrows(), total=len(manifest_df), desc=f"Processing {split_name}"):
            video_path = Path(row['video_path'])
            
            if not video_path.exists():
                print(f"   ⚠️  Video not found: {video_path}")
                continue
            
            # Create output filename
            output_filename = f"{row['word']}_{Path(video_path).stem}_processed.pt"
            output_path = split_output_dir / output_filename
            
            # Process video
            result = preprocessor.process_video(video_path, output_path)
            
            if result['success']:
                successful_count += 1
                total_extraction_rate += result['extraction_rate']
                
                processed_videos.append({
                    'video_path': str(output_path),
                    'original_path': str(video_path),
                    'word': row['word'],
                    'label': row['label'],
                    'split': split_name,
                    'speaker': row['speaker'],
                    'filename': row['filename'],
                    'processed_filename': output_filename,
                    'extraction_rate': result['extraction_rate']
                })
        
        # Save processed manifest
        if processed_videos:
            processed_df = pd.DataFrame(processed_videos)
            processed_manifest_path = output_base / f"{split_name}_processed_manifest.csv"
            processed_df.to_csv(processed_manifest_path, index=False)
            
            avg_extraction_rate = total_extraction_rate / successful_count if successful_count > 0 else 0
            
            print(f"   ✅ {split_name}: {successful_count}/{len(manifest_df)} videos processed")
            print(f"      Average lip extraction rate: {avg_extraction_rate:.2%}")
            print(f"      Manifest saved: {processed_manifest_path}")
            
            processing_results[split_name] = {
                'total_videos': len(manifest_df),
                'successful': successful_count,
                'success_rate': successful_count / len(manifest_df),
                'avg_extraction_rate': avg_extraction_rate,
                'manifest_path': str(processed_manifest_path)
            }
    
    # Create combined manifest
    all_processed = []
    for split_name in ['train', 'val', 'test']:
        processed_manifest_path = output_base / f"{split_name}_processed_manifest.csv"
        if processed_manifest_path.exists():
            split_df = pd.read_csv(processed_manifest_path)
            all_processed.append(split_df)
    
    if all_processed:
        combined_df = pd.concat(all_processed, ignore_index=True)
        combined_path = output_base / "combined_processed_manifest.csv"
        combined_df.to_csv(combined_path, index=False)
        print(f"\n📝 Combined manifest saved: {combined_path} ({len(combined_df)} videos)")
    
    # Save processing summary
    processing_summary = {
        'processing_timestamp': pd.Timestamp.now().isoformat(),
        'input_directory': str(input_base),
        'output_directory': str(output_base),
        'preprocessing_config': {
            'target_size': [112, 112],
            'target_fps': 25,
            'num_frames': 64,
            'normalization': 'z-score',
            'color_space': 'grayscale',
            'lip_extraction': 'MediaPipe_FaceMesh'
        },
        'results': processing_results
    }
    
    summary_path = output_base / "processing_summary.json"
    with open(summary_path, 'w') as f:
        json.dump(processing_summary, f, indent=2)
    
    # Final summary
    total_videos = sum(r['total_videos'] for r in processing_results.values())
    total_successful = sum(r['successful'] for r in processing_results.values())
    overall_success_rate = total_successful / total_videos if total_videos > 0 else 0
    
    print(f"\n📊 Processing Summary:")
    print(f"   Total videos: {total_videos}")
    print(f"   Successfully processed: {total_successful}")
    print(f"   Overall success rate: {overall_success_rate:.1%}")
    print(f"   Output directory: {output_base}")
    print(f"   Summary saved: {summary_path}")
    
    # Cleanup MediaPipe
    preprocessor.face_mesh.close()
    
    if overall_success_rate >= 0.9:
        print(f"\n✅ Preprocessing completed successfully!")
        print(f"🚀 Ready for LipNet training!")
        return True
    else:
        print(f"\n⚠️  Some videos failed to process.")
        print(f"💡 Check individual video issues and consider manual review.")
        return False

def main():
    """Main preprocessing function"""
    start_time = time.time()
    
    success = process_speaker_separated_datasets()
    
    processing_time = time.time() - start_time
    print(f"\n⏱️  Total processing time: {processing_time:.1f} seconds")
    
    return success

if __name__ == "__main__":
    success = main()
