#!/usr/bin/env python3
"""
Phase 0: 30-minute hard sanity check with tiny subset.
Must reach ~100% train accuracy on 10 clips per class (50 total).
Simple setup: Linear classifier + CrossEntropy, no fancy features.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.models.video as video_models
import torchvision.transforms as transforms
import pandas as pd
import numpy as np
from pathlib import Path
import json
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

class TinyDebugDataset(Dataset):
    """Minimal dataset for debugging - no fancy augmentation"""
    
    def __init__(self, video_files, labels, target_frames=16):
        self.video_files = video_files
        self.labels = labels
        self.target_frames = target_frames
        
        print(f"📊 TinyDebugDataset: {len(video_files)} videos")
        unique_labels, counts = np.unique(labels, return_counts=True)
        for label, count in zip(unique_labels, counts):
            print(f"   Class {label}: {count} videos")
    
    def __len__(self):
        return len(self.video_files)
    
    def __getitem__(self, idx):
        video_path = self.video_files[idx]
        label = self.labels[idx]
        
        # Load preprocessed tensor
        try:
            video_tensor = torch.load(video_path)
        except Exception as e:
            print(f"Error loading {video_path}: {e}")
            # Return dummy tensor
            video_tensor = torch.randn(1, 64, 112, 112)
        
        # Ensure correct shape
        if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
            video_tensor = video_tensor.squeeze(0)  # (T, H, W)
        elif video_tensor.dim() == 3:
            pass  # Already (T, H, W)
        else:
            print(f"Unexpected tensor shape: {video_tensor.shape}")
            video_tensor = torch.randn(64, 112, 112)
        
        # Sample exactly target_frames
        T, H, W = video_tensor.shape
        if T >= self.target_frames:
            # Uniform sampling
            indices = torch.linspace(0, T - 1, self.target_frames).long()
            video_tensor = video_tensor[indices]
        else:
            # Repeat frames to reach target
            repeat_factor = (self.target_frames + T - 1) // T
            video_tensor = video_tensor.repeat(repeat_factor, 1, 1)[:self.target_frames]
        
        # Convert grayscale to RGB: (T, H, W) -> (T, 3, H, W)
        video_tensor = video_tensor.unsqueeze(1).repeat(1, 3, 1, 1)
        
        # Simple normalization: [-1, 1] -> [0, 1] -> ImageNet norm
        video_tensor = (video_tensor + 1.0) / 2.0
        
        # Apply ImageNet normalization
        normalize = transforms.Normalize(
            mean=[0.485, 0.456, 0.406],
            std=[0.229, 0.224, 0.225]
        )
        
        for t in range(self.target_frames):
            video_tensor[t] = normalize(video_tensor[t])
        
        # Rearrange to (3, T, H, W) for R(2+1)D
        video_tensor = video_tensor.permute(1, 0, 2, 3)
        
        return video_tensor, torch.tensor(label, dtype=torch.long)

class SimpleR2Plus1DModel(nn.Module):
    """Simple R(2+1)D model with linear classifier - NO fancy features"""
    
    def __init__(self, num_classes=5):
        super().__init__()
        
        # Load pretrained R(2+1)D-18
        try:
            self.backbone = video_models.r2plus1d_18(weights='KINETICS400_V1')
        except TypeError:
            self.backbone = video_models.r2plus1d_18(pretrained=True)
        
        # Remove final classifier to get 512-dim features
        self.backbone.fc = nn.Identity()
        
        # Simple linear classifier
        self.classifier = nn.Linear(512, num_classes)
        
        print(f"🤖 SimpleR2Plus1DModel created")
        print(f"   Backbone: R(2+1)D-18")
        print(f"   Classifier: Linear(512 -> {num_classes})")
    
    def forward(self, x):
        # x: (batch, 3, T, H, W)
        features = self.backbone(x)  # (batch, 512)
        logits = self.classifier(features)
        return logits

def create_tiny_debug_dataset():
    """Create tiny dataset: 10 clips per class, excluding male 18-39"""
    print("🔍 Creating tiny debug dataset (excluding male 18-39)...")
    
    # Use existing processed data
    train_manifest_path = "data/5word_processed/train_processed_manifest.csv"
    val_manifest_path = "data/5word_processed/val_processed_manifest.csv"
    
    if not Path(train_manifest_path).exists():
        print("❌ Processed manifests not found, using raw video files")
        return create_tiny_from_raw_videos()
    
    # Load processed manifests
    train_df = pd.read_csv(train_manifest_path)
    val_df = pd.read_csv(val_manifest_path)
    all_df = pd.concat([train_df, val_df], ignore_index=True)
    
    print(f"📊 Total processed videos: {len(all_df)}")
    
    # Filter out male 18-39 if demographic info available
    if 'filename' in all_df.columns:
        # Filter based on filename patterns
        filtered_df = all_df[~all_df['filename'].str.contains('18to39.*male', case=False, na=False)]
        print(f"📊 After filtering male 18-39: {len(filtered_df)}")
    else:
        filtered_df = all_df
    
    # Sample 10 videos per class
    words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
    label_map = {word: i for i, word in enumerate(words)}
    
    tiny_videos = []
    tiny_labels = []
    
    for word in words:
        word_videos = filtered_df[filtered_df['word'] == word]
        
        if len(word_videos) >= 10:
            sampled = word_videos.sample(n=10, random_state=42)
        else:
            sampled = word_videos
            print(f"⚠️  Only {len(word_videos)} videos for {word}")
        
        for _, row in sampled.iterrows():
            if Path(row['video_path']).exists():
                tiny_videos.append(row['video_path'])
                tiny_labels.append(label_map[word])
    
    print(f"✅ Tiny dataset created: {len(tiny_videos)} videos")
    
    # Split into train/val (6 train, 2 val per class)
    train_videos, train_labels = [], []
    val_videos, val_labels = [], []
    
    for label in range(5):
        label_indices = [i for i, l in enumerate(tiny_labels) if l == label]
        
        # Take first 6 for train, rest for val
        train_indices = label_indices[:6]
        val_indices = label_indices[6:]
        
        for i in train_indices:
            train_videos.append(tiny_videos[i])
            train_labels.append(tiny_labels[i])
        
        for i in val_indices:
            val_videos.append(tiny_videos[i])
            val_labels.append(tiny_labels[i])
    
    print(f"📊 Train: {len(train_videos)} videos, Val: {len(val_videos)} videos")
    
    return train_videos, train_labels, val_videos, val_labels

def create_tiny_from_raw_videos():
    """Fallback: create tiny dataset from raw video files"""
    print("🔍 Creating tiny dataset from raw video files...")
    
    # Use TRAINING SET and VAL SET directories
    train_dir = Path("data/TRAINING SET")
    val_dir = Path("data/VAL SET")
    
    words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
    label_map = {word: i for i, word in enumerate(words)}
    
    all_videos = []
    all_labels = []
    
    # Collect videos from both directories
    for directory in [train_dir, val_dir]:
        if directory.exists():
            for video_file in directory.glob("*.mp4"):
                filename = video_file.name.lower()
                
                # Skip male 18-39 files
                if '18to39' in filename and 'male' in filename:
                    continue
                
                # Determine word
                for word in words:
                    if filename.startswith(word):
                        all_videos.append(str(video_file))
                        all_labels.append(label_map[word])
                        break
    
    print(f"📊 Found {len(all_videos)} raw videos")
    
    # Sample 10 per class
    tiny_videos = []
    tiny_labels = []
    
    for label in range(5):
        label_videos = [v for v, l in zip(all_videos, all_labels) if l == label]
        sampled = label_videos[:10]  # Take first 10
        
        tiny_videos.extend(sampled)
        tiny_labels.extend([label] * len(sampled))
    
    # Split train/val
    train_videos, train_labels = [], []
    val_videos, val_labels = [], []
    
    for label in range(5):
        label_indices = [i for i, l in enumerate(tiny_labels) if l == label]
        
        train_indices = label_indices[:6]
        val_indices = label_indices[6:]
        
        for i in train_indices:
            train_videos.append(tiny_videos[i])
            train_labels.append(tiny_labels[i])
        
        for i in val_indices:
            val_videos.append(tiny_videos[i])
            val_labels.append(tiny_labels[i])
    
    return train_videos, train_labels, val_videos, val_labels

def debug_batch_sanity_check(model, train_loader, device):
    """Print one batch verbatim to check for wiring issues"""
    print("\n🔍 BATCH SANITY CHECK")
    print("=" * 50)
    
    model.eval()
    xb, yb = next(iter(train_loader))
    xb, yb = xb.to(device), yb.to(device)
    
    print(f"Batch shape: {xb.shape}")
    print(f"Labels shape: {yb.shape}")
    print(f"Labels unique: {yb.unique().cpu().numpy()}")
    print(f"Labels counts: {torch.bincount(yb, minlength=5).cpu().numpy()}")
    
    with torch.no_grad():
        logits = model(xb)
        print(f"Logits shape: {logits.shape}")
        print(f"Logits min/max: {logits.min().item():.3f}/{logits.max().item():.3f}")
        
        pred = logits.argmax(1).cpu()
        print(f"Pred counts: {torch.bincount(pred, minlength=5).cpu().numpy()}")
        
        batch_acc = (pred == yb.cpu()).float().mean().item()
        print(f"Batch accuracy: {batch_acc:.3f}")
        
        # Check if model is predicting same class for everything
        if len(pred.unique()) == 1:
            print("⚠️  WARNING: Model predicting same class for all samples!")
        
        # Check logits distribution
        logits_std = logits.std(dim=1).mean().item()
        print(f"Logits std (across classes): {logits_std:.3f}")
        
        if logits_std < 0.1:
            print("⚠️  WARNING: Logits have very low variance - model may not be learning!")

def train_tiny_overfit():
    """Train on tiny dataset - must reach >95% train accuracy"""
    print("🎯 Phase 0: Tiny Overfit Test")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  Using device: {device}")
    
    # Create tiny dataset
    train_videos, train_labels, val_videos, val_labels = create_tiny_debug_dataset()
    
    # Create datasets
    train_dataset = TinyDebugDataset(train_videos, train_labels, target_frames=16)
    val_dataset = TinyDebugDataset(val_videos, val_labels, target_frames=16)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False, num_workers=0)
    
    # Create simple model
    model = SimpleR2Plus1DModel(num_classes=5)
    model.to(device)
    
    # Simple optimizer - NO scheduler
    optimizer = optim.AdamW(model.parameters(), lr=3e-4, weight_decay=1e-4)
    criterion = nn.CrossEntropyLoss()
    
    print(f"📊 Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Batch sanity check
    debug_batch_sanity_check(model, train_loader, device)
    
    # Training loop
    words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
    target_train_acc = 95.0
    
    for epoch in range(20):
        print(f"\n📅 Epoch {epoch+1}/20")
        
        # Training
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for batch_idx, (videos, labels) in enumerate(train_loader):
            videos, labels = videos.to(device), labels.to(device)
            
            optimizer.zero_grad()
            logits = model(videos)
            loss = criterion(logits, labels)
            loss.backward()
            
            # Check gradient norms
            total_norm = torch.sqrt(sum((p.grad.detach()**2).sum() for p in model.parameters() if p.grad is not None))
            
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            
            train_loss += loss.item()
            pred = logits.argmax(1)
            train_total += labels.size(0)
            train_correct += (pred == labels).sum().item()
            
            if batch_idx == 0:  # Print first batch details
                print(f"   Batch 0: loss={loss.item():.3f}, grad_norm={total_norm.item():.3f}")
                print(f"   Labels: {labels.cpu().numpy()}")
                print(f"   Preds:  {pred.cpu().numpy()}")
        
        # Validation
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for videos, labels in val_loader:
                videos, labels = videos.to(device), labels.to(device)
                
                logits = model(videos)
                loss = criterion(logits, labels)
                
                val_loss += loss.item()
                pred = logits.argmax(1)
                val_total += labels.size(0)
                val_correct += (pred == labels).sum().item()
                
                all_preds.extend(pred.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        train_acc = 100. * train_correct / train_total
        val_acc = 100. * val_correct / val_total
        
        print(f"📊 Results:")
        print(f"   Train Loss: {train_loss/len(train_loader):.4f} | Train Acc: {train_acc:.2f}%")
        print(f"   Val Loss: {val_loss/len(val_loader):.4f} | Val Acc: {val_acc:.2f}%")
        
        # Confusion matrix
        if len(all_preds) > 0:
            cm = confusion_matrix(all_labels, all_preds, labels=list(range(5)))
            print(f"   Val Confusion Matrix:")
            print("     ", " ".join([f"{word:>6}" for word in words]))
            for i, word in enumerate(words):
                print(f"{word:>6}: {' '.join([f'{cm[i,j]:>6}' for j in range(5)])}")
        
        # Check if target reached
        if train_acc >= target_train_acc:
            print(f"\n🎉 SUCCESS: Reached {train_acc:.2f}% train accuracy!")
            print("✅ Model can learn - no fundamental wiring bug")
            return True
        
        # Early warning if not learning
        if epoch >= 5 and train_acc < 50:
            print(f"\n⚠️  WARNING: Train accuracy still {train_acc:.2f}% after {epoch+1} epochs")
            print("🔍 Possible issues:")
            print("   - Data loading problem")
            print("   - Label mismatch")
            print("   - Gradient flow issue")
            print("   - Learning rate too low/high")
    
    print(f"\n❌ FAILED: Only reached {train_acc:.2f}% train accuracy")
    print("🐛 There is a fundamental bug in the training loop")
    return False

def main():
    """Main function for tiny overfit test"""
    print("🔍 Phase 0: 30-Minute Hard Sanity Check")
    print("=" * 60)
    
    success = train_tiny_overfit()
    
    if success:
        print("\n✅ PHASE 0 PASSED: Model can learn on tiny dataset")
        print("🔄 Ready to proceed to full dataset debugging")
    else:
        print("\n❌ PHASE 0 FAILED: Fundamental wiring bug detected")
        print("🔧 Fix the training loop before proceeding")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
