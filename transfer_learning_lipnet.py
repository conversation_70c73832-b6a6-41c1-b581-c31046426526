#!/usr/bin/env python3
"""
Transfer Learning LipNet using pretrained visual features.
Use pretrained ResNet features + simple classifier to achieve >80% accuracy.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import pandas as pd
import numpy as np
from pathlib import Path
import json
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, classification_report
import torchvision.models as models
import torchvision.transforms as transforms
import warnings
warnings.filterwarnings('ignore')

class TransferLearningDataset(Dataset):
    """Dataset for transfer learning approach"""
    
    def __init__(self, manifest_path, extract_features=False):
        self.manifest = pd.read_csv(manifest_path)
        self.extract_features = extract_features
        
        # Verify all files exist
        valid_rows = []
        for idx, row in self.manifest.iterrows():
            if Path(row['video_path']).exists():
                valid_rows.append(row)
        
        self.manifest = pd.DataFrame(valid_rows).reset_index(drop=True)
        
        print(f"📊 Dataset loaded: {len(self.manifest)} videos")
        class_counts = self.manifest['word'].value_counts()
        print(f"   Class distribution: {dict(class_counts)}")
        
        # Setup transforms for feature extraction
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    def __len__(self):
        return len(self.manifest)
    
    def extract_frame_features(self, video_tensor, feature_extractor):
        """Extract features from video frames using pretrained ResNet"""
        # video_tensor shape: (1, T, H, W) - grayscale
        features = []
        
        with torch.no_grad():
            for t in range(video_tensor.size(1)):
                frame = video_tensor[0, t]  # (H, W)
                
                # Convert grayscale to RGB
                frame_rgb = frame.repeat(3, 1, 1)  # (3, H, W)
                
                # Normalize to [0, 1] range
                frame_rgb = (frame_rgb + 3) / 6  # Assuming input was normalized to [-3, 3]
                frame_rgb = torch.clamp(frame_rgb, 0, 1)
                
                # Convert to PIL and apply transforms
                frame_pil = transforms.ToPILImage()(frame_rgb)
                frame_transformed = self.transform(frame_pil).unsqueeze(0)  # (1, 3, 224, 224)
                
                # Extract features
                frame_features = feature_extractor(frame_transformed)  # (1, 2048)
                features.append(frame_features.squeeze(0))  # (2048,)
        
        # Stack features: (T, 2048)
        features = torch.stack(features, dim=0)
        return features
    
    def __getitem__(self, idx):
        row = self.manifest.iloc[idx]
        
        # Load preprocessed tensor
        video_tensor = torch.load(row['video_path'])
        
        # Ensure correct shape: (1, T, H, W)
        if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
            pass  # Already correct
        elif video_tensor.dim() == 3:
            video_tensor = video_tensor.unsqueeze(0)
        else:
            raise ValueError(f"Unexpected tensor shape: {video_tensor.shape}")
        
        label = torch.tensor(row['label'], dtype=torch.long)
        
        if self.extract_features:
            return video_tensor, label
        else:
            return video_tensor, label

class FeatureExtractor(nn.Module):
    """ResNet-based feature extractor"""
    
    def __init__(self):
        super(FeatureExtractor, self).__init__()
        
        # Load pretrained ResNet50
        resnet = models.resnet50(pretrained=True)
        
        # Remove final classification layer
        self.features = nn.Sequential(*list(resnet.children())[:-1])
        
        # Freeze all parameters
        for param in self.features.parameters():
            param.requires_grad = False
        
        self.features.eval()
    
    def forward(self, x):
        # x: (batch, 3, 224, 224)
        features = self.features(x)  # (batch, 2048, 1, 1)
        features = features.view(features.size(0), -1)  # (batch, 2048)
        return features

class TransferLearningLipNet(nn.Module):
    """Simple classifier on top of pretrained features"""
    
    def __init__(self, num_classes=5, feature_dim=2048, hidden_dim=512):
        super(TransferLearningLipNet, self).__init__()
        
        # Temporal aggregation
        self.temporal_pool = nn.AdaptiveAvgPool1d(1)
        
        # Simple classifier
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(feature_dim, hidden_dim),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(hidden_dim),
            nn.Dropout(0.5),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize classifier weights"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, features):
        # features: (batch, T, feature_dim)
        
        # Temporal pooling
        features = features.permute(0, 2, 1)  # (batch, feature_dim, T)
        pooled = self.temporal_pool(features).squeeze(-1)  # (batch, feature_dim)
        
        # Classification
        output = self.classifier(pooled)
        
        return output

class TransferLearningTrainer:
    """Transfer learning trainer"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  Using device: {self.device}")
        
        self.batch_size = 16
        self.learning_rate = 0.001
        self.epochs = 100
        self.target_accuracy = 80.0
        
        # Create output directory
        self.output_dir = Path("artifacts/vsr_fasthead_v1")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🔄 Transfer learning setup:")
        print(f"   Batch size: {self.batch_size}")
        print(f"   Learning rate: {self.learning_rate}")
        print(f"   Target accuracy: {self.target_accuracy}%")
        
        # Initialize feature extractor
        self.feature_extractor = FeatureExtractor().to(self.device)
        print("✅ Pretrained ResNet50 feature extractor loaded")
    
    def extract_and_save_features(self, dataset, split_name):
        """Extract and save features for faster training"""
        features_dir = self.output_dir / "features"
        features_dir.mkdir(exist_ok=True)
        
        features_path = features_dir / f"{split_name}_features.pt"
        labels_path = features_dir / f"{split_name}_labels.pt"
        
        if features_path.exists() and labels_path.exists():
            print(f"✅ Loading cached {split_name} features...")
            features = torch.load(features_path)
            labels = torch.load(labels_path)
            return features, labels
        
        print(f"🔄 Extracting {split_name} features...")
        
        dataloader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=2)
        
        all_features = []
        all_labels = []
        
        self.feature_extractor.eval()
        
        for video_tensor, label in tqdm(dataloader, desc=f"Extracting {split_name}"):
            video_tensor = video_tensor.to(self.device)
            
            # Extract features for each frame
            batch_features = []
            for b in range(video_tensor.size(0)):
                video = video_tensor[b]  # (1, T, H, W)
                
                frame_features = []
                for t in range(video.size(1)):
                    frame = video[0, t]  # (H, W)
                    
                    # Convert grayscale to RGB
                    frame_rgb = frame.repeat(3, 1, 1)  # (3, H, W)
                    
                    # Normalize to [0, 1] range
                    frame_rgb = (frame_rgb + 3) / 6
                    frame_rgb = torch.clamp(frame_rgb, 0, 1)
                    
                    # Resize to 224x224
                    frame_resized = torch.nn.functional.interpolate(
                        frame_rgb.unsqueeze(0), size=(224, 224), mode='bilinear'
                    ).squeeze(0)
                    
                    # Normalize with ImageNet stats
                    normalize = transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
                    frame_normalized = normalize(frame_resized).unsqueeze(0)  # (1, 3, 224, 224)
                    
                    # Extract features
                    with torch.no_grad():
                        features = self.feature_extractor(frame_normalized)  # (1, 2048)
                    
                    frame_features.append(features.squeeze(0))  # (2048,)
                
                # Stack frame features: (T, 2048)
                video_features = torch.stack(frame_features, dim=0)
                batch_features.append(video_features)
            
            # Stack batch features: (batch, T, 2048)
            batch_features = torch.stack(batch_features, dim=0)
            
            all_features.append(batch_features.cpu())
            all_labels.append(label)
        
        # Concatenate all features and labels
        features = torch.cat(all_features, dim=0)  # (N, T, 2048)
        labels = torch.cat(all_labels, dim=0)  # (N,)
        
        # Save features
        torch.save(features, features_path)
        torch.save(labels, labels_path)
        
        print(f"✅ {split_name} features saved: {features.shape}")
        
        return features, labels
    
    def train(self):
        """Train transfer learning model"""
        print("🚀 Starting Transfer Learning Training")
        print("=" * 70)
        
        # Load datasets
        train_dataset = TransferLearningDataset(
            "data/speaker_separated_processed/train_processed_manifest.csv"
        )
        val_dataset = TransferLearningDataset(
            "data/speaker_separated_processed/val_processed_manifest.csv"
        )
        
        # Extract features
        train_features, train_labels = self.extract_and_save_features(train_dataset, "train")
        val_features, val_labels = self.extract_and_save_features(val_dataset, "val")
        
        # Create feature datasets
        train_feature_dataset = torch.utils.data.TensorDataset(train_features, train_labels)
        val_feature_dataset = torch.utils.data.TensorDataset(val_features, val_labels)
        
        # Create data loaders
        train_loader = DataLoader(
            train_feature_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=2,
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_feature_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=2,
            pin_memory=True
        )
        
        # Create model
        model = TransferLearningLipNet(num_classes=5, feature_dim=2048, hidden_dim=512)
        model.to(self.device)
        
        total_params = sum(p.numel() for p in model.parameters())
        print(f"🤖 Model parameters: {total_params:,}")
        
        # Optimizer and scheduler
        optimizer = optim.Adam(model.parameters(), lr=self.learning_rate, weight_decay=0.01)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='max', factor=0.5, patience=10, verbose=True
        )
        
        # Loss function
        criterion = nn.CrossEntropyLoss()
        
        # Training loop
        best_val_acc = 0.0
        patience = 20
        epochs_without_improvement = 0
        
        for epoch in range(self.epochs):
            print(f"\n📅 Epoch {epoch+1}/{self.epochs}")
            print("-" * 50)
            
            # Training
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for features, labels in tqdm(train_loader, desc="Training"):
                features, labels = features.to(self.device), labels.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(features)
                loss = criterion(outputs, labels)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = outputs.max(1)
                train_total += labels.size(0)
                train_correct += predicted.eq(labels).sum().item()
            
            # Validation
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            all_predictions = []
            all_labels = []
            
            with torch.no_grad():
                for features, labels in tqdm(val_loader, desc="Validation"):
                    features, labels = features.to(self.device), labels.to(self.device)
                    
                    outputs = model(features)
                    loss = criterion(outputs, labels)
                    
                    val_loss += loss.item()
                    _, predicted = outputs.max(1)
                    val_total += labels.size(0)
                    val_correct += predicted.eq(labels).sum().item()
                    
                    all_predictions.extend(predicted.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())
            
            train_acc = 100. * train_correct / train_total
            val_acc = 100. * val_correct / val_total
            
            print(f"📊 Results:")
            print(f"   Train Loss: {train_loss/len(train_loader):.4f} | Train Acc: {train_acc:.2f}%")
            print(f"   Val Loss: {val_loss/len(val_loader):.4f} | Val Acc: {val_acc:.2f}%")
            
            # Update scheduler
            scheduler.step(val_acc)
            
            # Check improvement
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                epochs_without_improvement = 0
                
                # Save best model
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'accuracy': val_acc,
                    'training_config': {
                        'batch_size': self.batch_size,
                        'learning_rate': self.learning_rate,
                        'architecture': 'TransferLearning'
                    }
                }, self.output_dir / 'transfer_learning_best.pth')
                
                print(f"🎉 NEW BEST: {val_acc:.2f}% validation accuracy!")
                
                # Print classification report
                words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
                report = classification_report(all_labels, all_predictions, target_names=words, digits=3)
                print(f"\n📋 Classification Report:")
                print(report)
                
                # Check if target achieved
                if val_acc >= self.target_accuracy:
                    print(f"\n🎯 TARGET ACHIEVED: {self.target_accuracy:.1f}% validation accuracy!")
                    print(f"   Final validation accuracy: {val_acc:.2f}%")
                    print(f"   🏆 SUCCESS with Transfer Learning!")
                    return True
                
            else:
                epochs_without_improvement += 1
            
            # Early stopping
            if epochs_without_improvement >= patience:
                print(f"\n⏹️  Early stopping: No improvement for {patience} epochs")
                break
        
        print(f"\n✅ Transfer Learning Training completed!")
        print(f"   Best validation accuracy: {best_val_acc:.2f}%")
        print(f"   Target achieved: {'YES! 🎉' if best_val_acc >= self.target_accuracy else 'Not yet'}")
        
        return best_val_acc >= self.target_accuracy

def main():
    """Main transfer learning function"""
    print("🎯 Transfer Learning LipNet Training")
    print("=" * 60)
    
    # Check if processed data exists
    train_manifest = Path("data/speaker_separated_processed/train_processed_manifest.csv")
    val_manifest = Path("data/speaker_separated_processed/val_processed_manifest.csv")
    
    if not train_manifest.exists() or not val_manifest.exists():
        print("❌ Processed dataset not found!")
        return False
    
    # Create transfer learning trainer
    trainer = TransferLearningTrainer()
    success = trainer.train()
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
