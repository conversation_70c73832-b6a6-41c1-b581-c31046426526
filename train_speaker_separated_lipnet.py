#!/usr/bin/env python3
"""
Train LipNet on speaker-separated datasets to achieve >80% validation accuracy.
Uses proper speaker separation to ensure generalization.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import pandas as pd
import numpy as np
from pathlib import Path
import json
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

class SpeakerSeparatedDataset(Dataset):
    """Dataset for speaker-separated LipNet training"""
    
    def __init__(self, manifest_path, augment=False, augmentation_strength=0.3):
        self.manifest = pd.read_csv(manifest_path)
        self.augment = augment
        self.augmentation_strength = augmentation_strength
        
        # Verify all files exist
        valid_rows = []
        for idx, row in self.manifest.iterrows():
            if Path(row['video_path']).exists():
                valid_rows.append(row)
            else:
                print(f"⚠️  Missing file: {row['video_path']}")
        
        self.manifest = pd.DataFrame(valid_rows).reset_index(drop=True)
        
        # Print dataset info
        print(f"📊 Dataset loaded: {len(self.manifest)} videos")
        class_counts = self.manifest['word'].value_counts()
        print(f"   Class distribution: {dict(class_counts)}")
        speaker_counts = self.manifest['speaker'].value_counts()
        print(f"   Speakers: {len(speaker_counts)} unique speakers")
    
    def __len__(self):
        return len(self.manifest)
    
    def __getitem__(self, idx):
        row = self.manifest.iloc[idx]
        
        # Load preprocessed tensor
        video_tensor = torch.load(row['video_path'])
        
        # Ensure correct shape: (1, T, H, W)
        if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
            # Already correct shape
            pass
        elif video_tensor.dim() == 3:
            # Add channel dimension
            video_tensor = video_tensor.unsqueeze(0)
        else:
            raise ValueError(f"Unexpected tensor shape: {video_tensor.shape}")
        
        # Apply augmentation if enabled
        if self.augment:
            video_tensor = self.apply_augmentation(video_tensor)
        
        label = torch.tensor(row['label'], dtype=torch.long)
        
        return video_tensor, label
    
    def apply_augmentation(self, video_tensor):
        """Apply data augmentation with controlled strength"""
        # Random brightness/contrast
        if torch.rand(1) < 0.6:
            brightness = (torch.rand(1) - 0.5) * self.augmentation_strength
            contrast = 1.0 + (torch.rand(1) - 0.5) * self.augmentation_strength
            video_tensor = video_tensor * contrast + brightness
        
        # Random horizontal flip
        if torch.rand(1) < 0.5:
            video_tensor = torch.flip(video_tensor, dims=[-1])
        
        # Random temporal jitter (slight frame reordering)
        if torch.rand(1) < 0.3:
            num_frames = video_tensor.size(1)
            # Small random permutation of adjacent frames
            jitter_strength = max(1, int(num_frames * 0.05))  # 5% of frames
            for _ in range(jitter_strength):
                i = torch.randint(0, num_frames - 1, (1,)).item()
                if torch.rand(1) < 0.5:
                    # Swap adjacent frames
                    video_tensor[:, [i, i+1]] = video_tensor[:, [i+1, i]]
        
        # Add small amount of Gaussian noise
        if torch.rand(1) < 0.4:
            noise = torch.randn_like(video_tensor) * 0.02
            video_tensor = video_tensor + noise
        
        # Clamp to reasonable range
        video_tensor = torch.clamp(video_tensor, -3, 3)
        
        return video_tensor

class OptimizedLipNet(nn.Module):
    """Optimized LipNet architecture for 5-word classification"""
    
    def __init__(self, num_classes=5, dropout=0.4):
        super(OptimizedLipNet, self).__init__()
        
        # 3D CNN backbone - optimized for 64 frames
        self.conv3d_1 = nn.Conv3d(1, 32, kernel_size=(3, 7, 7), stride=(1, 2, 2), padding=(1, 3, 3))
        self.bn3d_1 = nn.BatchNorm3d(32)
        self.pool3d_1 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        
        self.conv3d_2 = nn.Conv3d(32, 64, kernel_size=(3, 5, 5), stride=(1, 1, 1), padding=(1, 2, 2))
        self.bn3d_2 = nn.BatchNorm3d(64)
        self.pool3d_2 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        
        self.conv3d_3 = nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))
        self.bn3d_3 = nn.BatchNorm3d(128)
        self.pool3d_3 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        
        # Calculate the size after conv layers
        # Input: (1, 64, 112, 112)
        # After conv3d_1 + pool: (32, 32, 28, 28)
        # After conv3d_2 + pool: (64, 16, 14, 14)
        # After conv3d_3 + pool: (128, 8, 7, 7)

        # Temporal modeling with BiLSTM
        self.lstm_input_size = 128 * 7 * 7  # Flattened spatial dimensions = 6272
        self.lstm = nn.LSTM(
            input_size=self.lstm_input_size,
            hidden_size=256,
            num_layers=2,
            batch_first=True,
            dropout=dropout,
            bidirectional=True
        )
        
        # Attention mechanism
        self.attention = nn.MultiheadAttention(
            embed_dim=512,  # 256 * 2 (bidirectional)
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            nn.Linear(128, num_classes)
        )
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize model weights"""
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm3d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LSTM):
                for name, param in m.named_parameters():
                    if 'weight' in name:
                        nn.init.orthogonal_(param)
                    elif 'bias' in name:
                        nn.init.constant_(param, 0)
    
    def forward(self, x):
        # Input: (batch, 1, T, H, W)
        batch_size = x.size(0)
        
        # 3D CNN feature extraction
        x = torch.relu(self.bn3d_1(self.conv3d_1(x)))
        x = self.pool3d_1(x)
        
        x = torch.relu(self.bn3d_2(self.conv3d_2(x)))
        x = self.pool3d_2(x)
        
        x = torch.relu(self.bn3d_3(self.conv3d_3(x)))
        x = self.pool3d_3(x)
        
        # Reshape for LSTM: (batch, time, features)
        # x shape: (batch, 128, 8, 14, 14)
        x = x.permute(0, 2, 1, 3, 4)  # (batch, 8, 128, 14, 14)
        x = x.contiguous().view(batch_size, x.size(1), -1)  # (batch, 8, 128*14*14)
        
        # BiLSTM
        lstm_out, _ = self.lstm(x)  # (batch, 8, 512)
        
        # Self-attention
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)  # (batch, 8, 512)
        
        # Global average pooling over time dimension
        pooled = torch.mean(attn_out, dim=1)  # (batch, 512)
        
        # Classification
        output = self.classifier(pooled)
        
        return output

class SpeakerSeparatedTrainer:
    """Trainer for speaker-separated LipNet"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  Using device: {self.device}")
        
        # Training parameters optimized for speaker separation
        self.batch_size = 16  # Smaller batch for better generalization
        self.learning_rate = 0.001
        self.epochs = 50
        self.target_accuracy = 80.0
        self.patience = 10  # Early stopping patience
        
        # Create output directory
        self.output_dir = Path("artifacts/vsr_fasthead_v1")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup model and training
        self.setup_model()
        self.setup_data_loaders()
        self.setup_training()
        
        # Training state
        self.best_accuracy = 0.0
        self.best_epoch = 0
        self.training_history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': [],
            'learning_rates': []
        }
    
    def setup_model(self):
        """Setup optimized LipNet model"""
        print("🤖 Setting up Optimized LipNet...")
        
        self.model = OptimizedLipNet(num_classes=5, dropout=0.4)
        self.model.to(self.device)
        
        # Print model info
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        print(f"   Total parameters: {total_params:,}")
        print(f"   Trainable parameters: {trainable_params:,}")
        print(f"   Architecture: 3D CNN + BiLSTM + Attention")
    
    def setup_data_loaders(self):
        """Setup data loaders for speaker-separated datasets"""
        print("📊 Setting up speaker-separated data loaders...")
        
        # Load datasets
        train_dataset = SpeakerSeparatedDataset(
            "data/speaker_separated_processed/train_processed_manifest.csv",
            augment=True,
            augmentation_strength=0.3
        )
        
        val_dataset = SpeakerSeparatedDataset(
            "data/speaker_separated_processed/val_processed_manifest.csv",
            augment=False
        )
        
        # Create data loaders
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=2,
            pin_memory=True,
            drop_last=True  # For consistent batch sizes
        )
        
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=2,
            pin_memory=True
        )
        
        print(f"   Training batches: {len(self.train_loader)}")
        print(f"   Validation batches: {len(self.val_loader)}")
    
    def setup_training(self):
        """Setup optimizer, scheduler, and loss function"""
        print("⚙️  Setting up training components...")
        
        # Optimizer with weight decay for regularization
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.learning_rate,
            weight_decay=0.01,
            betas=(0.9, 0.999)
        )
        
        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer,
            T_0=10,
            T_mult=2,
            eta_min=1e-6
        )
        
        # Loss function with label smoothing
        self.criterion = nn.CrossEntropyLoss(label_smoothing=0.1)
        
        # Mixed precision training
        self.scaler = torch.cuda.amp.GradScaler() if self.device.type == 'cuda' else None

    def train_epoch(self, epoch):
        """Train for one epoch"""
        self.model.train()

        total_loss = 0.0
        correct = 0
        total = 0

        pbar = tqdm(self.train_loader, desc=f"Epoch {epoch+1}")

        for batch_idx, (videos, labels) in enumerate(pbar):
            videos, labels = videos.to(self.device), labels.to(self.device)

            self.optimizer.zero_grad()

            if self.scaler:
                with torch.cuda.amp.autocast():
                    outputs = self.model(videos)
                    loss = self.criterion(outputs, labels)

                self.scaler.scale(loss).backward()
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                outputs = self.model(videos)
                loss = self.criterion(outputs, labels)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                self.optimizer.step()

            # Statistics
            total_loss += loss.item()
            _, predicted = outputs.max(1)
            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()

            # Update progress bar
            current_lr = self.optimizer.param_groups[0]['lr']
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*correct/total:.2f}%',
                'LR': f'{current_lr:.6f}'
            })

        avg_loss = total_loss / len(self.train_loader)
        accuracy = 100. * correct / total

        return avg_loss, accuracy

    def validate(self):
        """Validate the model"""
        self.model.eval()

        total_loss = 0.0
        correct = 0
        total = 0
        all_predictions = []
        all_labels = []

        with torch.no_grad():
            for videos, labels in tqdm(self.val_loader, desc="Validating"):
                videos, labels = videos.to(self.device), labels.to(self.device)

                if self.scaler:
                    with torch.cuda.amp.autocast():
                        outputs = self.model(videos)
                        loss = self.criterion(outputs, labels)
                else:
                    outputs = self.model(videos)
                    loss = self.criterion(outputs, labels)

                total_loss += loss.item()
                _, predicted = outputs.max(1)
                total += labels.size(0)
                correct += predicted.eq(labels).sum().item()

                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())

        avg_loss = total_loss / len(self.val_loader)
        accuracy = 100. * correct / total

        return avg_loss, accuracy, all_predictions, all_labels

    def save_checkpoint(self, epoch, accuracy, is_best=False):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'accuracy': accuracy,
            'best_accuracy': self.best_accuracy,
            'training_history': self.training_history,
            'model_config': {
                'num_classes': 5,
                'dropout': 0.4,
                'architecture': 'OptimizedLipNet'
            }
        }

        # Save regular checkpoint
        checkpoint_path = self.output_dir / f'checkpoint_epoch_{epoch+1}.pth'
        torch.save(checkpoint, checkpoint_path)

        # Save best model
        if is_best:
            best_path = self.output_dir / 'lipnet_5word_speaker_separated.pth'
            torch.save(checkpoint, best_path)
            print(f"💾 New best model saved: {accuracy:.2f}% accuracy")

    def plot_training_curves(self):
        """Plot training curves"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        epochs = range(1, len(self.training_history['train_loss']) + 1)

        # Loss curves
        ax1.plot(epochs, self.training_history['train_loss'], 'b-', label='Training Loss')
        ax1.plot(epochs, self.training_history['val_loss'], 'r-', label='Validation Loss')
        ax1.set_title('Training and Validation Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)

        # Accuracy curves
        ax2.plot(epochs, self.training_history['train_acc'], 'b-', label='Training Accuracy')
        ax2.plot(epochs, self.training_history['val_acc'], 'r-', label='Validation Accuracy')
        ax2.axhline(y=self.target_accuracy, color='g', linestyle='--', label=f'Target ({self.target_accuracy}%)')
        ax2.set_title('Training and Validation Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy (%)')
        ax2.legend()
        ax2.grid(True)

        # Learning rate
        ax3.plot(epochs, self.training_history['learning_rates'], 'g-')
        ax3.set_title('Learning Rate Schedule')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.set_yscale('log')
        ax3.grid(True)

        # Best accuracy indicator
        ax4.bar(['Best Validation'], [self.best_accuracy], color='green' if self.best_accuracy >= self.target_accuracy else 'orange')
        ax4.axhline(y=self.target_accuracy, color='red', linestyle='--', label=f'Target ({self.target_accuracy}%)')
        ax4.set_title('Best Validation Accuracy')
        ax4.set_ylabel('Accuracy (%)')
        ax4.legend()
        ax4.grid(True)

        plt.tight_layout()
        plot_path = self.output_dir / 'training_curves.png'
        plt.savefig(plot_path, dpi=150, bbox_inches='tight')
        plt.close()

        print(f"📊 Training curves saved: {plot_path}")

    def plot_confusion_matrix(self, predictions, labels, epoch):
        """Plot confusion matrix"""
        words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
        cm = confusion_matrix(labels, predictions)

        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=words, yticklabels=words)
        plt.title(f'Confusion Matrix - Epoch {epoch+1} (Val Acc: {100*accuracy_score(labels, predictions):.1f}%)')
        plt.xlabel('Predicted')
        plt.ylabel('Actual')

        cm_path = self.output_dir / f'confusion_matrix_epoch_{epoch+1}.png'
        plt.savefig(cm_path, dpi=150, bbox_inches='tight')
        plt.close()

        return cm

    def train(self):
        """Main training loop"""
        print("🚀 Starting Speaker-Separated LipNet Training")
        print("=" * 70)

        start_time = time.time()
        epochs_without_improvement = 0

        for epoch in range(self.epochs):
            print(f"\n📅 Epoch {epoch+1}/{self.epochs}")
            print("-" * 50)

            # Training
            epoch_start = time.time()
            train_loss, train_acc = self.train_epoch(epoch)

            # Validation
            val_loss, val_acc, predictions, labels = self.validate()

            # Update scheduler
            self.scheduler.step()
            current_lr = self.optimizer.param_groups[0]['lr']

            epoch_time = time.time() - epoch_start

            # Update training history
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_acc'].append(train_acc)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_acc'].append(val_acc)
            self.training_history['learning_rates'].append(current_lr)

            # Print epoch results
            print(f"\n📊 Epoch {epoch+1} Results:")
            print(f"   Train Loss: {train_loss:.4f} | Train Acc: {train_acc:.2f}%")
            print(f"   Val Loss: {val_loss:.4f} | Val Acc: {val_acc:.2f}%")
            print(f"   Learning Rate: {current_lr:.6f}")
            print(f"   Time: {epoch_time:.1f}s")

            # Check if this is the best model
            is_best = val_acc > self.best_accuracy
            if is_best:
                self.best_accuracy = val_acc
                self.best_epoch = epoch
                epochs_without_improvement = 0
                print(f"🎉 New best validation accuracy: {val_acc:.2f}%")

                # Print detailed classification report
                words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
                report = classification_report(labels, predictions, target_names=words, digits=3)
                print(f"\n📋 Classification Report (Epoch {epoch+1}):")
                print(report)

                # Plot confusion matrix for best model
                self.plot_confusion_matrix(predictions, labels, epoch)

            else:
                epochs_without_improvement += 1

            # Save checkpoint
            self.save_checkpoint(epoch, val_acc, is_best)

            # Check for target accuracy
            if val_acc >= self.target_accuracy:
                print(f"\n🎯 Target accuracy {self.target_accuracy:.1f}% achieved!")
                print(f"   Final validation accuracy: {val_acc:.2f}%")
                break

            # Early stopping
            if epochs_without_improvement >= self.patience:
                print(f"\n⏹️  Early stopping: No improvement for {self.patience} epochs")
                print(f"   Best accuracy: {self.best_accuracy:.2f}% at epoch {self.best_epoch+1}")
                break

            # Progress indicator
            if val_acc < self.target_accuracy:
                remaining = self.target_accuracy - val_acc
                print(f"   📈 Progress: {remaining:.1f}% to target")

        total_time = time.time() - start_time

        # Final results
        print(f"\n✅ Training completed!")
        print(f"   Best validation accuracy: {self.best_accuracy:.2f}%")
        print(f"   Best epoch: {self.best_epoch+1}")
        print(f"   Target achieved: {'Yes' if self.best_accuracy >= self.target_accuracy else 'No'}")
        print(f"   Total training time: {total_time/60:.1f} minutes")

        # Plot training curves
        self.plot_training_curves()

        # Save final training summary
        summary = {
            'best_accuracy': self.best_accuracy,
            'best_epoch': self.best_epoch + 1,
            'target_accuracy': self.target_accuracy,
            'target_achieved': self.best_accuracy >= self.target_accuracy,
            'total_epochs': len(self.training_history['val_acc']),
            'total_training_time_minutes': total_time / 60,
            'final_train_acc': self.training_history['train_acc'][-1],
            'final_val_acc': self.training_history['val_acc'][-1],
            'training_config': {
                'batch_size': self.batch_size,
                'learning_rate': self.learning_rate,
                'epochs': self.epochs,
                'patience': self.patience,
                'device': str(self.device)
            },
            'model_config': {
                'architecture': 'OptimizedLipNet',
                'num_classes': 5,
                'dropout': 0.4
            }
        }

        summary_path = self.output_dir / 'training_summary.json'
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)

        print(f"📄 Training summary saved: {summary_path}")

        return self.best_accuracy >= self.target_accuracy

def test_on_test_set():
    """Test the best model on the test set for final evaluation"""
    print("\n🧪 Testing on Test Set for Final Evaluation")
    print("=" * 60)

    # Load best model
    model_path = Path("artifacts/vsr_fasthead_v1/lipnet_5word_speaker_separated.pth")
    if not model_path.exists():
        print(f"❌ Best model not found: {model_path}")
        return False

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # Load model
    checkpoint = torch.load(model_path, map_location=device)
    model = OptimizedLipNet(num_classes=5, dropout=0.4)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    model.eval()

    print(f"✅ Loaded best model (Val Acc: {checkpoint['best_accuracy']:.2f}%)")

    # Load test dataset
    test_dataset = SpeakerSeparatedDataset(
        "data/speaker_separated_processed/test_processed_manifest.csv",
        augment=False
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=16,
        shuffle=False,
        num_workers=2,
        pin_memory=True
    )

    # Test the model
    correct = 0
    total = 0
    all_predictions = []
    all_labels = []

    with torch.no_grad():
        for videos, labels in tqdm(test_loader, desc="Testing"):
            videos, labels = videos.to(device), labels.to(device)

            outputs = model(videos)
            _, predicted = outputs.max(1)

            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()

            all_predictions.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    test_accuracy = 100. * correct / total

    print(f"\n📊 Test Set Results:")
    print(f"   Test Accuracy: {test_accuracy:.2f}%")
    print(f"   Test Videos: {total}")

    # Detailed classification report
    words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
    report = classification_report(all_labels, all_predictions, target_names=words, digits=3)
    print(f"\n📋 Test Set Classification Report:")
    print(report)

    # Confusion matrix
    cm = confusion_matrix(all_labels, all_predictions)
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
               xticklabels=words, yticklabels=words)
    plt.title(f'Test Set Confusion Matrix (Accuracy: {test_accuracy:.1f}%)')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')

    output_dir = Path("artifacts/vsr_fasthead_v1")
    cm_path = output_dir / 'test_set_confusion_matrix.png'
    plt.savefig(cm_path, dpi=150, bbox_inches='tight')
    plt.close()

    print(f"📊 Test confusion matrix saved: {cm_path}")

    # Save test results
    test_results = {
        'test_accuracy': test_accuracy,
        'test_videos': total,
        'validation_accuracy': checkpoint['best_accuracy'],
        'generalization_gap': checkpoint['best_accuracy'] - test_accuracy,
        'classification_report': report,
        'test_timestamp': pd.Timestamp.now().isoformat()
    }

    test_results_path = output_dir / 'test_results.json'
    with open(test_results_path, 'w') as f:
        json.dump(test_results, f, indent=2, default=str)

    print(f"📄 Test results saved: {test_results_path}")

    return test_accuracy >= 80.0

def main():
    """Main function"""
    print("🎯 Speaker-Separated LipNet Training for >80% Accuracy")
    print("=" * 80)

    # Check if processed data exists
    processed_dir = Path("data/speaker_separated_processed")
    if not processed_dir.exists():
        print("❌ Processed data not found. Run preprocess_speaker_separated.py first!")
        return False

    # Create trainer and start training
    trainer = SpeakerSeparatedTrainer()
    training_success = trainer.train()

    if training_success:
        print(f"\n🎉 SUCCESS: >80% validation accuracy achieved!")

        # Test on test set for final evaluation
        test_success = test_on_test_set()

        if test_success:
            print(f"\n🏆 EXCELLENT: >80% test accuracy achieved!")
            print(f"   Model generalizes well to unseen speakers!")
        else:
            print(f"\n⚠️  Test accuracy <80%, but validation target was met.")
            print(f"   Model may need more diverse training data.")

        print(f"\n📁 Model saved: artifacts/vsr_fasthead_v1/lipnet_5word_speaker_separated.pth")

    else:
        print(f"\n⚠️  Target accuracy not reached. Best: {trainer.best_accuracy:.2f}%")
        print(f"💡 Consider: More epochs, different architecture, or more data")

    return training_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
