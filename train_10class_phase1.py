#!/usr/bin/env python3
"""
Phase 1: Frozen Backbone Training for 10-Class Lipreading
========================================================

Phase 1 (20 minutes): Frozen backbone training
- Freeze R(2+1)D backbone, train only BiGRU + classifier head
- Optimizer: <PERSON>(lr=3e-4, weight_decay=1e-4)
- Loss: Focal loss with computed class weights for imbalanced data
- Regularization: Label smoothing=0.1, dropout=0.3
- Sampling: Class-balanced sampling, batch_size=16

Usage:
    python train_10class_phase1.py --epochs 10 --batch_size 16 --lr 3e-4
"""

import os
import json
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
import numpy as np
from pathlib import Path
import argparse
from typing import Dict, List, Tuple
import logging
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# Import our modules
from r2plus1d_10class_model import R2Plus1D10ClassModel
from data_loader_10class import create_10class_dataloaders

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FocalLoss(nn.Module):
    """Focal Loss for handling class imbalance"""
    
    def __init__(self, alpha: torch.Tensor = None, gamma: float = 2.0, reduction: str = 'mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        ce_loss = nn.functional.cross_entropy(inputs, targets, weight=self.alpha, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class LabelSmoothingCrossEntropy(nn.Module):
    """Label smoothing cross entropy loss"""
    
    def __init__(self, smoothing: float = 0.1):
        super(LabelSmoothingCrossEntropy, self).__init__()
        self.smoothing = smoothing
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        log_prob = nn.functional.log_softmax(inputs, dim=-1)
        weight = inputs.new_ones(inputs.size()) * self.smoothing / (inputs.size(-1) - 1.)
        weight.scatter_(-1, targets.unsqueeze(-1), (1. - self.smoothing))
        loss = (-weight * log_prob).sum(dim=-1).mean()
        return loss

class Phase1Trainer:
    """Phase 1 trainer for frozen backbone training"""
    
    def __init__(self, 
                 model: R2Plus1D10ClassModel,
                 train_loader,
                 val_loader,
                 test_loader,
                 class_to_idx: Dict[str, int],
                 idx_to_class: Dict[int, str],
                 device: torch.device,
                 output_dir: str = "artifacts/10class_phase1"):
        
        self.model = model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.test_loader = test_loader
        self.class_to_idx = class_to_idx
        self.idx_to_class = idx_to_class
        self.device = device
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup tensorboard
        self.writer = SummaryWriter(self.output_dir / "tensorboard")
        
        # Training history
        self.history = {
            'train_loss': [], 'train_acc': [], 'train_f1': [],
            'val_loss': [], 'val_acc': [], 'val_f1': [],
            'epoch_times': []
        }
        
        logger.info(f"📁 Phase 1 output directory: {self.output_dir}")
    
    def setup_training(self, lr: float = 3e-4, weight_decay: float = 1e-4, 
                      use_focal_loss: bool = True, label_smoothing: float = 0.1):
        """Setup training components"""
        
        # Freeze backbone
        self.model.freeze_backbone()
        
        # Calculate class weights for imbalanced data
        class_counts = torch.zeros(len(self.class_to_idx))
        for _, labels, _ in self.train_loader:
            for label in labels:
                class_counts[label] += 1
        
        class_weights = 1.0 / class_counts
        class_weights = class_weights / class_weights.sum() * len(class_weights)
        class_weights = class_weights.to(self.device)
        
        logger.info(f"📊 Class weights:")
        for idx, weight in enumerate(class_weights):
            logger.info(f"   {self.idx_to_class[idx]}: {weight:.3f}")
        
        # Setup loss function
        if use_focal_loss:
            self.criterion = FocalLoss(alpha=class_weights, gamma=2.0)
            logger.info(f"🎯 Using Focal Loss with gamma=2.0")
        else:
            if label_smoothing > 0:
                self.criterion = LabelSmoothingCrossEntropy(smoothing=label_smoothing)
                logger.info(f"🎯 Using Label Smoothing Cross Entropy (smoothing={label_smoothing})")
            else:
                self.criterion = nn.CrossEntropyLoss(weight=class_weights)
                logger.info(f"🎯 Using Weighted Cross Entropy")
        
        # Setup optimizer (only trainable parameters)
        trainable_params = [p for p in self.model.parameters() if p.requires_grad]
        self.optimizer = optim.Adam(trainable_params, lr=lr, weight_decay=weight_decay)
        
        # Setup scheduler
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='max', factor=0.5, patience=3, verbose=True
        )
        
        logger.info(f"⚙️ Training setup complete:")
        logger.info(f"   Learning rate: {lr}")
        logger.info(f"   Weight decay: {weight_decay}")
        logger.info(f"   Trainable parameters: {sum(p.numel() for p in trainable_params):,}")
    
    def train_epoch(self, epoch: int) -> Dict[str, float]:
        """Train for one epoch"""
        self.model.train()
        
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        start_time = time.time()
        
        for batch_idx, (videos, labels, metadata) in enumerate(self.train_loader):
            videos = videos.to(self.device)
            labels = labels.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            outputs = self.model(videos)
            loss = self.criterion(outputs, labels)
            
            # Backward pass
            loss.backward()
            self.optimizer.step()
            
            # Statistics
            total_loss += loss.item()
            predictions = outputs.argmax(dim=1)
            all_predictions.extend(predictions.cpu().numpy())
            all_targets.extend(labels.cpu().numpy())
            
            # Log progress
            if batch_idx % 10 == 0:
                logger.info(f"   Batch {batch_idx}/{len(self.train_loader)}: loss={loss.item():.4f}")
        
        # Calculate metrics
        epoch_time = time.time() - start_time
        avg_loss = total_loss / len(self.train_loader)
        accuracy = accuracy_score(all_targets, all_predictions)
        f1 = f1_score(all_targets, all_predictions, average='macro')
        
        return {
            'loss': avg_loss,
            'accuracy': accuracy,
            'f1': f1,
            'time': epoch_time
        }
    
    def validate_epoch(self, epoch: int) -> Dict[str, float]:
        """Validate for one epoch"""
        self.model.eval()
        
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for videos, labels, metadata in self.val_loader:
                videos = videos.to(self.device)
                labels = labels.to(self.device)
                
                outputs = self.model(videos)
                loss = self.criterion(outputs, labels)
                
                total_loss += loss.item()
                predictions = outputs.argmax(dim=1)
                all_predictions.extend(predictions.cpu().numpy())
                all_targets.extend(labels.cpu().numpy())
        
        # Calculate metrics
        avg_loss = total_loss / len(self.val_loader)
        accuracy = accuracy_score(all_targets, all_predictions)
        f1 = f1_score(all_targets, all_predictions, average='macro')
        
        return {
            'loss': avg_loss,
            'accuracy': accuracy,
            'f1': f1,
            'predictions': all_predictions,
            'targets': all_targets
        }
    
    def train(self, epochs: int = 10, target_time_minutes: int = 20):
        """Train the model"""
        logger.info(f"🚀 Starting Phase 1 training for {epochs} epochs (target: {target_time_minutes} minutes)")
        
        best_val_f1 = 0.0
        best_epoch = 0
        training_start_time = time.time()
        
        for epoch in range(epochs):
            epoch_start_time = time.time()
            
            logger.info(f"\n📊 Epoch {epoch+1}/{epochs}")
            
            # Train
            train_metrics = self.train_epoch(epoch)
            
            # Validate
            val_metrics = self.validate_epoch(epoch)
            
            # Update scheduler
            self.scheduler.step(val_metrics['f1'])
            
            # Log metrics
            epoch_time = time.time() - epoch_start_time
            total_time = time.time() - training_start_time
            
            logger.info(f"   Train: loss={train_metrics['loss']:.4f}, acc={train_metrics['accuracy']:.4f}, f1={train_metrics['f1']:.4f}")
            logger.info(f"   Val:   loss={val_metrics['loss']:.4f}, acc={val_metrics['accuracy']:.4f}, f1={val_metrics['f1']:.4f}")
            logger.info(f"   Time: {epoch_time:.1f}s (total: {total_time/60:.1f}min)")
            
            # Save metrics
            self.history['train_loss'].append(train_metrics['loss'])
            self.history['train_acc'].append(train_metrics['accuracy'])
            self.history['train_f1'].append(train_metrics['f1'])
            self.history['val_loss'].append(val_metrics['loss'])
            self.history['val_acc'].append(val_metrics['accuracy'])
            self.history['val_f1'].append(val_metrics['f1'])
            self.history['epoch_times'].append(epoch_time)
            
            # Tensorboard logging
            self.writer.add_scalar('Loss/Train', train_metrics['loss'], epoch)
            self.writer.add_scalar('Loss/Val', val_metrics['loss'], epoch)
            self.writer.add_scalar('Accuracy/Train', train_metrics['accuracy'], epoch)
            self.writer.add_scalar('Accuracy/Val', val_metrics['accuracy'], epoch)
            self.writer.add_scalar('F1/Train', train_metrics['f1'], epoch)
            self.writer.add_scalar('F1/Val', val_metrics['f1'], epoch)
            
            # Save best model
            if val_metrics['f1'] > best_val_f1:
                best_val_f1 = val_metrics['f1']
                best_epoch = epoch
                
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_f1': val_metrics['f1'],
                    'val_acc': val_metrics['accuracy'],
                    'class_to_idx': self.class_to_idx,
                    'idx_to_class': self.idx_to_class
                }, self.output_dir / "best_model.pth")
                
                logger.info(f"   💾 New best model saved! (F1: {best_val_f1:.4f})")
            
            # Check time limit
            if total_time > target_time_minutes * 60:
                logger.info(f"⏰ Time limit reached ({target_time_minutes} minutes)")
                break
        
        logger.info(f"\n🎉 Phase 1 training complete!")
        logger.info(f"   Best validation F1: {best_val_f1:.4f} (epoch {best_epoch+1})")
        logger.info(f"   Total training time: {total_time/60:.1f} minutes")
        
        # Save training history
        with open(self.output_dir / "training_history.json", 'w') as f:
            json.dump(self.history, f, indent=2)
        
        return best_val_f1

def main():
    parser = argparse.ArgumentParser(description="Phase 1: Frozen Backbone Training")
    parser.add_argument("--processed_dir", default="data/10class_processed", help="Processed data directory")
    parser.add_argument("--output_dir", default="artifacts/10class_phase1", help="Output directory")
    parser.add_argument("--epochs", type=int, default=10, help="Number of epochs")
    parser.add_argument("--batch_size", type=int, default=16, help="Batch size")
    parser.add_argument("--lr", type=float, default=3e-4, help="Learning rate")
    parser.add_argument("--weight_decay", type=float, default=1e-4, help="Weight decay")
    parser.add_argument("--target_time", type=int, default=20, help="Target training time (minutes)")
    
    args = parser.parse_args()
    
    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"🖥️ Using device: {device}")
    
    # Create data loaders
    logger.info(f"📊 Loading 10-class dataset...")
    train_loader, val_loader, test_loader, class_to_idx, idx_to_class = create_10class_dataloaders(
        processed_dir=args.processed_dir,
        batch_size=args.batch_size,
        num_workers=4
    )
    
    # Create model
    logger.info(f"🤖 Creating R(2+1)D + BiGRU model...")
    model = R2Plus1D10ClassModel(num_classes=len(class_to_idx))
    model.print_model_info()
    
    # Create trainer
    trainer = Phase1Trainer(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        test_loader=test_loader,
        class_to_idx=class_to_idx,
        idx_to_class=idx_to_class,
        device=device,
        output_dir=args.output_dir
    )
    
    # Setup training
    trainer.setup_training(
        lr=args.lr,
        weight_decay=args.weight_decay,
        use_focal_loss=True,
        label_smoothing=0.1
    )
    
    # Train
    best_f1 = trainer.train(epochs=args.epochs, target_time_minutes=args.target_time)
    
    logger.info(f"🎯 Phase 1 Results:")
    logger.info(f"   Best validation F1: {best_f1:.4f}")
    logger.info(f"   Target F1 (>0.60): {'✅ PASS' if best_f1 > 0.60 else '❌ FAIL'}")

if __name__ == "__main__":
    main()
