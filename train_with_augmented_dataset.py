#!/usr/bin/env python3
"""
Train LipNet with dramatically expanded augmented dataset.
Focus on achieving >80% validation accuracy with 2,261 training videos.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import pandas as pd
import numpy as np
from pathlib import Path
import json
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

class AugmentedDataset(Dataset):
    """Dataset for augmented training data"""
    
    def __init__(self, manifest_path):
        self.manifest = pd.read_csv(manifest_path)
        
        # Verify all files exist
        valid_rows = []
        for idx, row in self.manifest.iterrows():
            if Path(row['video_path']).exists():
                valid_rows.append(row)
        
        self.manifest = pd.DataFrame(valid_rows).reset_index(drop=True)
        
        # Print dataset info
        print(f"📊 Dataset loaded: {len(self.manifest)} videos")
        class_counts = self.manifest['word'].value_counts()
        print(f"   Class distribution: {dict(class_counts)}")
    
    def __len__(self):
        return len(self.manifest)
    
    def __getitem__(self, idx):
        row = self.manifest.iloc[idx]
        
        # Load preprocessed tensor
        video_tensor = torch.load(row['video_path'])
        
        # Ensure correct shape: (1, T, H, W)
        if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
            pass  # Already correct
        elif video_tensor.dim() == 3:
            video_tensor = video_tensor.unsqueeze(0)
        else:
            raise ValueError(f"Unexpected tensor shape: {video_tensor.shape}")
        
        label = torch.tensor(row['label'], dtype=torch.long)
        
        return video_tensor, label

class SimplifiedLipNet(nn.Module):
    """Simplified LipNet for faster training with large dataset"""
    
    def __init__(self, num_classes=5, dropout=0.3):
        super(SimplifiedLipNet, self).__init__()
        
        # Simplified 3D CNN backbone
        self.conv3d_1 = nn.Conv3d(1, 32, kernel_size=(3, 7, 7), stride=(1, 2, 2), padding=(1, 3, 3))
        self.bn3d_1 = nn.BatchNorm3d(32)
        self.pool3d_1 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        
        self.conv3d_2 = nn.Conv3d(32, 64, kernel_size=(3, 5, 5), stride=(1, 1, 1), padding=(1, 2, 2))
        self.bn3d_2 = nn.BatchNorm3d(64)
        self.pool3d_2 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        
        self.conv3d_3 = nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))
        self.bn3d_3 = nn.BatchNorm3d(128)
        self.pool3d_3 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        
        # Global average pooling instead of LSTM for speed
        self.global_avg_pool = nn.AdaptiveAvgPool3d((1, 1, 1))
        
        # Simple classification head
        self.classifier = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(128, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            nn.Linear(128, num_classes)
        )
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize model weights"""
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm3d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # Input: (batch, 1, T, H, W)
        
        # 3D CNN feature extraction
        x = torch.relu(self.bn3d_1(self.conv3d_1(x)))
        x = self.pool3d_1(x)
        
        x = torch.relu(self.bn3d_2(self.conv3d_2(x)))
        x = self.pool3d_2(x)
        
        x = torch.relu(self.bn3d_3(self.conv3d_3(x)))
        x = self.pool3d_3(x)
        
        # Global average pooling
        x = self.global_avg_pool(x)
        x = x.view(x.size(0), -1)  # Flatten
        
        # Classification
        x = self.classifier(x)
        
        return x

class AugmentedTrainer:
    """Trainer for augmented dataset"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  Using device: {self.device}")
        
        # Training parameters optimized for large dataset
        self.batch_size = 32  # Larger batch size for stability
        self.learning_rate = 0.001
        self.epochs = 20  # Fewer epochs needed with large dataset
        self.target_accuracy = 80.0
        
        # Create output directory
        self.output_dir = Path("artifacts/vsr_fasthead_v1")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup model and training
        self.setup_model()
        self.setup_data_loaders()
        self.setup_training()
        
        # Training state
        self.best_accuracy = 0.0
        self.training_history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': []
        }
    
    def setup_model(self):
        """Setup simplified LipNet model"""
        print("🤖 Setting up Simplified LipNet...")
        
        self.model = SimplifiedLipNet(num_classes=5, dropout=0.3)
        self.model.to(self.device)
        
        # Print model info
        total_params = sum(p.numel() for p in self.model.parameters())
        print(f"   Total parameters: {total_params:,}")
        print(f"   Architecture: Simplified 3D CNN + Global Pooling")
    
    def setup_data_loaders(self):
        """Setup data loaders"""
        print("📊 Setting up data loaders...")
        
        # Augmented training dataset
        train_dataset = AugmentedDataset("data/speaker_separated_augmented/augmented_manifest.csv")
        
        # Original validation dataset (no augmentation)
        val_dataset = AugmentedDataset("data/speaker_separated_processed/val_processed_manifest.csv")
        
        # Create data loaders
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=4,
            pin_memory=True,
            drop_last=True
        )
        
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=2,
            pin_memory=True
        )
        
        print(f"   Training batches: {len(self.train_loader)}")
        print(f"   Validation batches: {len(self.val_loader)}")
    
    def setup_training(self):
        """Setup optimizer, scheduler, and loss function"""
        print("⚙️  Setting up training components...")
        
        # Optimizer
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=self.learning_rate,
            weight_decay=1e-4
        )
        
        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.StepLR(
            self.optimizer,
            step_size=7,
            gamma=0.5
        )
        
        # Loss function
        self.criterion = nn.CrossEntropyLoss()
    
    def train_epoch(self, epoch):
        """Train for one epoch"""
        self.model.train()
        
        total_loss = 0.0
        correct = 0
        total = 0
        
        pbar = tqdm(self.train_loader, desc=f"Epoch {epoch+1}")
        
        for batch_idx, (videos, labels) in enumerate(pbar):
            videos, labels = videos.to(self.device), labels.to(self.device)
            
            self.optimizer.zero_grad()
            
            outputs = self.model(videos)
            loss = self.criterion(outputs, labels)
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            
            self.optimizer.step()
            
            # Statistics
            total_loss += loss.item()
            _, predicted = outputs.max(1)
            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()
            
            # Update progress bar
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*correct/total:.2f}%'
            })
        
        avg_loss = total_loss / len(self.train_loader)
        accuracy = 100. * correct / total
        
        return avg_loss, accuracy
    
    def validate(self):
        """Validate the model"""
        self.model.eval()
        
        total_loss = 0.0
        correct = 0
        total = 0
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            for videos, labels in tqdm(self.val_loader, desc="Validating"):
                videos, labels = videos.to(self.device), labels.to(self.device)
                
                outputs = self.model(videos)
                loss = self.criterion(outputs, labels)
                
                total_loss += loss.item()
                _, predicted = outputs.max(1)
                total += labels.size(0)
                correct += predicted.eq(labels).sum().item()
                
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        avg_loss = total_loss / len(self.val_loader)
        accuracy = 100. * correct / total
        
        return avg_loss, accuracy, all_predictions, all_labels
    
    def save_checkpoint(self, epoch, accuracy, is_best=False):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'accuracy': accuracy,
            'model_class': 'SimplifiedLipNet'
        }
        
        # Save best model
        if is_best:
            best_path = self.output_dir / 'lipnet_5word_speaker_separated.pth'
            torch.save(checkpoint, best_path)
            print(f"💾 New best model saved: {accuracy:.2f}% accuracy")
    
    def train(self):
        """Main training loop"""
        print("🚀 Starting training with dramatically expanded dataset...")
        print("=" * 70)
        
        for epoch in range(self.epochs):
            print(f"\n📅 Epoch {epoch+1}/{self.epochs}")
            print("-" * 50)
            
            # Training
            start_time = time.time()
            train_loss, train_acc = self.train_epoch(epoch)
            
            # Validation
            val_loss, val_acc, predictions, labels = self.validate()
            
            # Update scheduler
            self.scheduler.step()
            
            epoch_time = time.time() - start_time
            
            # Update training history
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_acc'].append(train_acc)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_acc'].append(val_acc)
            
            # Print epoch results
            print(f"\n📊 Epoch {epoch+1} Results:")
            print(f"   Train Loss: {train_loss:.4f} | Train Acc: {train_acc:.2f}%")
            print(f"   Val Loss: {val_loss:.4f} | Val Acc: {val_acc:.2f}%")
            print(f"   Time: {epoch_time:.1f}s")
            
            # Check if this is the best model
            is_best = val_acc > self.best_accuracy
            if is_best:
                self.best_accuracy = val_acc
                print(f"🎉 New best validation accuracy: {val_acc:.2f}%")
                
                # Print classification report for best epochs
                words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
                report = classification_report(labels, predictions, target_names=words)
                print(f"\n📋 Classification Report (Epoch {epoch+1}):")
                print(report)
            
            # Save checkpoint
            self.save_checkpoint(epoch, val_acc, is_best)
            
            # Check for target accuracy
            if val_acc >= self.target_accuracy:
                print(f"\n🎯 Target accuracy {self.target_accuracy:.1f}% achieved!")
                print(f"   Final validation accuracy: {val_acc:.2f}%")
                break
        
        # Final results
        print(f"\n✅ Training completed!")
        print(f"   Best validation accuracy: {self.best_accuracy:.2f}%")
        print(f"   Target achieved: {'Yes' if self.best_accuracy >= self.target_accuracy else 'No'}")
        
        return self.best_accuracy >= self.target_accuracy

def main():
    """Main function"""
    print("🎯 Training with Dramatically Expanded Dataset")
    print("=" * 60)
    
    # Check if augmented data exists
    augmented_manifest = Path("data/speaker_separated_augmented/augmented_manifest.csv")
    if not augmented_manifest.exists():
        print("❌ Augmented dataset not found. Run aggressive_data_augmentation.py first!")
        return False
    
    # Create trainer and start training
    trainer = AugmentedTrainer()
    success = trainer.train()
    
    if success:
        print(f"\n🎉 SUCCESS: >80% validation accuracy achieved!")
        print(f"📁 Model saved: {trainer.output_dir}/lipnet_5word_speaker_separated.pth")
    else:
        print(f"\n⚠️  Target accuracy not reached. Best: {trainer.best_accuracy:.2f}%")
        print(f"💡 The dramatically expanded dataset should help achieve better results")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
