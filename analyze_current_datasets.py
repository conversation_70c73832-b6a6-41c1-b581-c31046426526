#!/usr/bin/env python3
"""
Comprehensive analysis of the current speaker-separated datasets.
"""

import os
import cv2
import pandas as pd
import numpy as np
from pathlib import Path
import json
from collections import defaultdict, Counter
import re

def extract_speaker_id_from_filename(filename):
    """Extract speaker ID from filename like 'word X.mp4' where X is the video number"""
    match = re.search(r'(\w+)\s+(\d+)\.mp4', filename)
    if match:
        word = match.group(1)
        video_num = int(match.group(2))
        return f"speaker_{video_num:02d}"
    return "unknown"

def analyze_dataset_comprehensive(dataset_path, dataset_name):
    """Comprehensive analysis of a dataset folder"""
    dataset_path = Path(dataset_path)
    
    if not dataset_path.exists():
        print(f"❌ Dataset not found: {dataset_path}")
        return None
    
    print(f"\n📁 Analyzing {dataset_name}: {dataset_path}")
    print("-" * 60)
    
    # Find all MP4 files
    video_files = list(dataset_path.glob("*.mp4"))
    print(f"   Total videos found: {len(video_files)}")
    
    if not video_files:
        print("   ⚠️  No videos found!")
        return None
    
    # Analyze each video
    videos_data = []
    word_counts = Counter()
    speaker_counts = Counter()
    speaker_words = defaultdict(set)
    
    for video_file in video_files:
        filename = video_file.name
        
        # Extract word and speaker
        match = re.search(r'(\w+)\s+(\d+)\.mp4', filename)
        if match:
            word = match.group(1).lower()
            video_num = int(match.group(2))
            speaker_id = f"speaker_{video_num:02d}"
        else:
            print(f"   ⚠️  Could not parse filename: {filename}")
            continue
        
        # Analyze video properties
        try:
            cap = cv2.VideoCapture(str(video_file))
            if cap.isOpened():
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                duration = frame_count / fps if fps > 0 else 0
                cap.release()
                
                video_props = {
                    'frame_count': frame_count,
                    'fps': fps,
                    'width': width,
                    'height': height,
                    'duration': duration
                }
            else:
                video_props = None
        except Exception as e:
            print(f"   ⚠️  Error analyzing {filename}: {e}")
            video_props = None
        
        videos_data.append({
            'filename': filename,
            'word': word,
            'speaker_id': speaker_id,
            'video_num': video_num,
            'path': str(video_file),
            'properties': video_props
        })
        
        word_counts[word] += 1
        speaker_counts[speaker_id] += 1
        speaker_words[speaker_id].add(word)
    
    # Print word distribution
    print(f"\n   📊 Word Distribution:")
    expected_words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
    for word in expected_words:
        count = word_counts.get(word, 0)
        print(f"      {word}: {count} videos")
    
    # Check for missing words
    missing_words = [word for word in expected_words if word_counts.get(word, 0) == 0]
    if missing_words:
        print(f"   ❌ Missing words: {missing_words}")
    else:
        print(f"   ✅ All 5 words present")
    
    # Print speaker distribution
    print(f"\n   👥 Speaker Distribution:")
    print(f"      Total unique speakers: {len(speaker_counts)}")
    
    # Show top speakers
    top_speakers = speaker_counts.most_common(10)
    for speaker_id, count in top_speakers:
        words_for_speaker = sorted(list(speaker_words[speaker_id]))
        print(f"      {speaker_id}: {count} videos - {words_for_speaker}")
    
    # Video quality summary
    valid_props = [v['properties'] for v in videos_data if v['properties']]
    if valid_props:
        avg_duration = np.mean([p['duration'] for p in valid_props])
        avg_fps = np.mean([p['fps'] for p in valid_props])
        avg_width = np.mean([p['width'] for p in valid_props])
        avg_height = np.mean([p['height'] for p in valid_props])
        
        print(f"\n   🎥 Video Quality:")
        print(f"      Average duration: {avg_duration:.2f}s")
        print(f"      Average FPS: {avg_fps:.1f}")
        print(f"      Average resolution: {avg_width:.0f}x{avg_height:.0f}")
    
    return {
        'dataset_name': dataset_name,
        'total_videos': len(videos_data),
        'videos_data': videos_data,
        'word_counts': dict(word_counts),
        'speaker_counts': dict(speaker_counts),
        'speaker_words': {k: list(v) for k, v in speaker_words.items()},
        'missing_words': missing_words,
        'all_speakers': list(speaker_counts.keys())
    }

def check_speaker_separation_detailed(train_data, val_data, test_data):
    """Detailed check of speaker separation"""
    print(f"\n🔍 Detailed Speaker Separation Analysis")
    print("=" * 60)
    
    if not all([train_data, val_data, test_data]):
        print("❌ Missing dataset information")
        return False
    
    train_speakers = set(train_data['all_speakers'])
    val_speakers = set(val_data['all_speakers'])
    test_speakers = set(test_data['all_speakers'])
    
    print(f"   Training speakers ({len(train_speakers)}): {sorted(list(train_speakers))}")
    print(f"   Validation speakers ({len(val_speakers)}): {sorted(list(val_speakers))}")
    print(f"   Test speakers ({len(test_speakers)}): {sorted(list(test_speakers))}")
    
    # Check overlaps
    train_val_overlap = train_speakers & val_speakers
    train_test_overlap = train_speakers & test_speakers
    val_test_overlap = val_speakers & test_speakers
    
    print(f"\n   🔄 Speaker Overlaps:")
    print(f"      Train-Val: {len(train_val_overlap)} speakers")
    if train_val_overlap:
        print(f"         {sorted(list(train_val_overlap))}")
    
    print(f"      Train-Test: {len(train_test_overlap)} speakers")
    if train_test_overlap:
        print(f"         {sorted(list(train_test_overlap))}")
    
    print(f"      Val-Test: {len(val_test_overlap)} speakers")
    if val_test_overlap:
        print(f"         {sorted(list(val_test_overlap))}")
    
    perfect_separation = not (train_val_overlap or train_test_overlap or val_test_overlap)
    
    print(f"\n   ✅ Perfect Speaker Separation: {'Yes' if perfect_separation else 'No'}")
    
    # If not perfect, suggest fixes
    if not perfect_separation:
        print(f"\n   💡 Suggested Fixes:")
        all_overlapping = train_val_overlap | train_test_overlap | val_test_overlap
        print(f"      - Move overlapping speakers to single dataset: {sorted(list(all_overlapping))}")
        print(f"      - Or reassign videos to ensure speaker separation")
    
    return perfect_separation

def generate_corrected_manifests(train_data, val_data, test_data):
    """Generate manifest files for the current datasets"""
    print(f"\n📝 Generating Manifest Files")
    print("=" * 40)
    
    output_dir = Path("data/current_dataset_manifests")
    output_dir.mkdir(exist_ok=True)
    
    words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
    
    # Generate manifests for each split
    for split_name, data in [('train', train_data), ('val', val_data), ('test', test_data)]:
        if not data:
            continue
            
        manifest_entries = []
        for video in data['videos_data']:
            manifest_entries.append({
                'video_path': video['path'],
                'word': video['word'],
                'label': words.index(video['word']) if video['word'] in words else -1,
                'split': split_name,
                'speaker': video['speaker_id'],
                'filename': video['filename'],
                'video_num': video['video_num']
            })
        
        # Save manifest
        manifest_df = pd.DataFrame(manifest_entries)
        manifest_path = output_dir / f"{split_name}_manifest.csv"
        manifest_df.to_csv(manifest_path, index=False)
        
        print(f"   ✅ {split_name.capitalize()} manifest: {manifest_path} ({len(manifest_df)} videos)")
    
    # Combined manifest
    all_manifests = []
    for split_name, data in [('train', train_data), ('val', val_data), ('test', test_data)]:
        if not data:
            continue
        for video in data['videos_data']:
            all_manifests.append({
                'video_path': video['path'],
                'word': video['word'],
                'label': words.index(video['word']) if video['word'] in words else -1,
                'split': split_name,
                'speaker': video['speaker_id'],
                'filename': video['filename'],
                'video_num': video['video_num']
            })
    
    if all_manifests:
        combined_df = pd.DataFrame(all_manifests)
        combined_path = output_dir / "combined_manifest.csv"
        combined_df.to_csv(combined_path, index=False)
        print(f"   ✅ Combined manifest: {combined_path} ({len(combined_df)} videos)")
    
    return output_dir

def main():
    """Main analysis function"""
    print("🔍 Comprehensive Current Dataset Analysis")
    print("=" * 70)
    
    # Analyze each dataset
    train_data = analyze_dataset_comprehensive("data/TRAINING SET", "Training Set")
    val_data = analyze_dataset_comprehensive("data/VAL SET", "Validation Set")
    test_data = analyze_dataset_comprehensive("data/TEST SET", "Test Set")
    
    # Check speaker separation
    speaker_separation_ok = check_speaker_separation_detailed(train_data, val_data, test_data)
    
    # Generate manifests
    manifest_dir = generate_corrected_manifests(train_data, val_data, test_data)
    
    # Overall summary
    print(f"\n📊 Overall Dataset Summary")
    print("=" * 50)
    
    total_videos = 0
    total_speakers = set()
    
    for name, data in [("Training", train_data), ("Validation", val_data), ("Test", test_data)]:
        if data:
            total_videos += data['total_videos']
            total_speakers.update(data['all_speakers'])
            print(f"   {name}: {data['total_videos']} videos, {len(data['all_speakers'])} speakers")
            
            # Check word balance
            missing = data['missing_words']
            if missing:
                print(f"      ❌ Missing words: {missing}")
            else:
                print(f"      ✅ All 5 words present")
    
    print(f"\n   📈 Totals:")
    print(f"      Total videos: {total_videos}")
    print(f"      Unique speakers: {len(total_speakers)}")
    print(f"      Perfect speaker separation: {'✅ Yes' if speaker_separation_ok else '❌ No'}")
    
    # Save results
    results = {
        'train_data': train_data,
        'val_data': val_data,
        'test_data': test_data,
        'speaker_separation_perfect': speaker_separation_ok,
        'total_videos': total_videos,
        'total_speakers': len(total_speakers),
        'manifest_directory': str(manifest_dir)
    }
    
    results_path = Path("current_dataset_analysis.json")
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Analysis saved: {results_path}")
    
    # Recommendations
    print(f"\n💡 Next Steps:")
    if speaker_separation_ok and all(data and not data['missing_words'] for data in [train_data, val_data, test_data]):
        print(f"   ✅ Datasets are ready for training!")
        print(f"   🚀 Proceed with preprocessing pipeline")
    else:
        if not speaker_separation_ok:
            print(f"   🔧 Fix speaker separation issues first")
        if any(data and data['missing_words'] for data in [train_data, val_data, test_data]):
            print(f"   📝 Address missing words in datasets")
        print(f"   ⚠️  Consider dataset reorganization")
    
    return results

if __name__ == "__main__":
    results = main()
