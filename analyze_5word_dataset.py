#!/usr/bin/env python3
"""
Analyze the new 5-word dataset in 'data/cropped videos 1.9.25/' folder.
Examine video format, dimensions, frame rate, quality, and create manifest files.
"""

import os
import cv2
import pandas as pd
import numpy as np
from pathlib import Path
import json
from collections import defaultdict
import random

def analyze_video_properties(video_path):
    """Analyze a single video file properties"""
    try:
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            return None
            
        # Get video properties
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps if fps > 0 else 0
        
        # Read first frame to check quality
        ret, first_frame = cap.read()
        first_frame_quality = None
        if ret:
            # Calculate basic quality metrics
            gray = cv2.cvtColor(first_frame, cv2.COLOR_BGR2GRAY)
            brightness = np.mean(gray)
            contrast = np.std(gray)
            first_frame_quality = {
                'brightness': brightness,
                'contrast': contrast,
                'has_content': brightness > 10  # Basic check for non-black frame
            }
        
        cap.release()
        
        return {
            'frame_count': frame_count,
            'fps': fps,
            'width': width,
            'height': height,
            'duration': duration,
            'quality': first_frame_quality
        }
    except Exception as e:
        print(f"Error analyzing {video_path}: {e}")
        return None

def create_speaker_wise_splits(video_data, train_ratio=0.8, val_ratio=0.2):
    """Create speaker-wise train/val splits to prevent data leakage"""
    
    # For this dataset, we'll use video numbers as proxy for speakers
    # Videos 1-16 for training, 17-20 for validation (80/20 split)
    train_videos = []
    val_videos = []
    
    for word, videos in video_data.items():
        # Sort videos by number
        sorted_videos = sorted(videos, key=lambda x: int(x['video_path'].stem.split()[-1]))
        
        # Split: first 16 for training, last 4 for validation
        train_count = int(len(sorted_videos) * train_ratio)
        
        train_videos.extend(sorted_videos[:train_count])
        val_videos.extend(sorted_videos[train_count:])
    
    return train_videos, val_videos

def main():
    """Main analysis function"""
    
    dataset_path = Path("data/cropped videos 1.9.25")
    
    if not dataset_path.exists():
        print(f"❌ Dataset path {dataset_path} does not exist!")
        return
    
    print("🔍 Analyzing 5-word dataset...")
    print("=" * 50)
    
    # Expected words
    expected_words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
    
    # Collect all video data
    video_data = defaultdict(list)
    analysis_results = {
        'dataset_summary': {},
        'video_properties': {},
        'quality_analysis': {},
        'recommendations': []
    }
    
    total_videos = 0
    
    for word in expected_words:
        word_path = dataset_path / word
        if not word_path.exists():
            print(f"⚠️  Missing folder for word: {word}")
            continue
            
        # Find all MP4 files
        video_files = list(word_path.glob("*.mp4"))
        print(f"📁 {word}: {len(video_files)} videos")
        
        word_properties = []
        
        for video_file in video_files:
            # Analyze video properties
            props = analyze_video_properties(video_file)
            if props:
                video_info = {
                    'word': word,
                    'video_path': video_file,
                    'video_name': video_file.name,
                    'properties': props
                }
                video_data[word].append(video_info)
                word_properties.append(props)
                total_videos += 1
        
        # Analyze word-level statistics
        if word_properties:
            analysis_results['video_properties'][word] = {
                'count': len(word_properties),
                'avg_frame_count': np.mean([p['frame_count'] for p in word_properties]),
                'avg_fps': np.mean([p['fps'] for p in word_properties]),
                'avg_width': np.mean([p['width'] for p in word_properties]),
                'avg_height': np.mean([p['height'] for p in word_properties]),
                'avg_duration': np.mean([p['duration'] for p in word_properties]),
                'avg_brightness': np.mean([p['quality']['brightness'] for p in word_properties if p['quality']]),
                'avg_contrast': np.mean([p['quality']['contrast'] for p in word_properties if p['quality']])
            }
    
    # Overall dataset summary
    analysis_results['dataset_summary'] = {
        'total_videos': total_videos,
        'words': list(video_data.keys()),
        'videos_per_word': {word: len(videos) for word, videos in video_data.items()},
        'balanced': all(len(videos) == 20 for videos in video_data.values())
    }
    
    # Print analysis results
    print(f"\n📊 Dataset Summary:")
    print(f"   Total videos: {total_videos}")
    print(f"   Words: {list(video_data.keys())}")
    print(f"   Videos per word: {dict(analysis_results['dataset_summary']['videos_per_word'])}")
    print(f"   Balanced dataset: {analysis_results['dataset_summary']['balanced']}")
    
    print(f"\n🎥 Video Properties Analysis:")
    for word, props in analysis_results['video_properties'].items():
        print(f"   {word}:")
        print(f"     Avg frames: {props['avg_frame_count']:.1f}")
        print(f"     Avg FPS: {props['avg_fps']:.1f}")
        print(f"     Avg dimensions: {props['avg_width']:.0f}x{props['avg_height']:.0f}")
        print(f"     Avg duration: {props['avg_duration']:.2f}s")
        print(f"     Avg brightness: {props['avg_brightness']:.1f}")
        print(f"     Avg contrast: {props['avg_contrast']:.1f}")
    
    # Create train/val splits
    print(f"\n📋 Creating train/validation splits...")
    train_videos, val_videos = create_speaker_wise_splits(video_data)
    
    print(f"   Training videos: {len(train_videos)}")
    print(f"   Validation videos: {len(val_videos)}")
    
    # Create manifest files
    print(f"\n📝 Creating manifest files...")
    
    # Create output directory
    output_dir = Path("data/5word_dataset")
    output_dir.mkdir(exist_ok=True)
    
    # Training manifest
    train_manifest = []
    for video_info in train_videos:
        train_manifest.append({
            'video_path': str(video_info['video_path']),
            'word': video_info['word'],
            'label': expected_words.index(video_info['word']),
            'split': 'train',
            'speaker': f"speaker_{video_info['video_name'].split()[-1].split('.')[0]}",  # Use video number as speaker ID
            'frame_count': video_info['properties']['frame_count'],
            'fps': video_info['properties']['fps'],
            'duration': video_info['properties']['duration']
        })
    
    # Validation manifest
    val_manifest = []
    for video_info in val_videos:
        val_manifest.append({
            'video_path': str(video_info['video_path']),
            'word': video_info['word'],
            'label': expected_words.index(video_info['word']),
            'split': 'val',
            'speaker': f"speaker_{video_info['video_name'].split()[-1].split('.')[0]}",
            'frame_count': video_info['properties']['frame_count'],
            'fps': video_info['properties']['fps'],
            'duration': video_info['properties']['duration']
        })
    
    # Save manifests
    train_df = pd.DataFrame(train_manifest)
    val_df = pd.DataFrame(val_manifest)
    
    train_df.to_csv(output_dir / "train_manifest.csv", index=False)
    val_df.to_csv(output_dir / "val_manifest.csv", index=False)
    
    # Combined manifest
    combined_df = pd.concat([train_df, val_df], ignore_index=True)
    combined_df.to_csv(output_dir / "combined_manifest.csv", index=False)
    
    print(f"   ✅ Saved train_manifest.csv ({len(train_df)} videos)")
    print(f"   ✅ Saved val_manifest.csv ({len(val_df)} videos)")
    print(f"   ✅ Saved combined_manifest.csv ({len(combined_df)} videos)")
    
    # Save analysis results
    with open(output_dir / "dataset_analysis.json", 'w') as f:
        # Convert Path objects to strings for JSON serialization
        serializable_results = analysis_results.copy()
        for word in serializable_results['video_properties']:
            serializable_results['video_properties'][word] = dict(serializable_results['video_properties'][word])
        
        json.dump(serializable_results, f, indent=2, default=str)
    
    print(f"   ✅ Saved dataset_analysis.json")
    
    # Generate recommendations
    recommendations = []
    
    # Check if preprocessing is needed
    avg_width = np.mean([props['avg_width'] for props in analysis_results['video_properties'].values()])
    avg_height = np.mean([props['avg_height'] for props in analysis_results['video_properties'].values()])
    avg_fps = np.mean([props['avg_fps'] for props in analysis_results['video_properties'].values()])
    
    if avg_width != 112 or avg_height != 112:
        recommendations.append(f"Resize videos from {avg_width:.0f}x{avg_height:.0f} to 112x112 for LipNet")
    
    if abs(avg_fps - 25) > 1:
        recommendations.append(f"Normalize frame rate from {avg_fps:.1f} to 25 FPS")
    
    recommendations.append("Convert videos to grayscale for LipNet processing")
    recommendations.append("Extract exactly 16 frames per video for training")
    recommendations.append("Apply z-score normalization to pixel values")
    
    print(f"\n💡 Preprocessing Recommendations:")
    for i, rec in enumerate(recommendations, 1):
        print(f"   {i}. {rec}")
    
    print(f"\n✅ Dataset analysis complete!")
    print(f"📁 Results saved to: {output_dir}")
    
    return analysis_results, train_df, val_df

if __name__ == "__main__":
    main()
