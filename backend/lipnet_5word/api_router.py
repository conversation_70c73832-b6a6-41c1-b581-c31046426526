"""
FastAPI router for LipNet 5-Word classification endpoints.

This module provides REST API endpoints for the 5-word ICU anchor classification.
"""

import os
import logging
import tempfile
from pathlib import Path
from typing import Dict, Optional

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import JSONResponse

from .infer import LipNet5WordInference, get_inference

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/lipnet5", tags=["LipNet 5-Word Classification"])

@router.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        inference = get_inference()
        model_info = inference.get_model_info()
        return {
            "status": "healthy",
            "model_loaded": model_info["model_loaded"],
            "device": model_info["device"],
            "words": model_info["words"]
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={"status": "unhealthy", "error": str(e)}
        )

@router.get("/info")
async def get_model_info():
    """Get detailed model information."""
    try:
        inference = get_inference()
        return inference.get_model_info()
    except Exception as e:
        logger.error(f"Failed to get model info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/predict")
async def predict_5word(file: UploadFile = File(...)):
    """
    Predict 5-word classification from video.
    
    Args:
        file: Video file (MP4, AVI, MOV)
        
    Returns:
        Prediction result with word, confidence, and action
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    # Validate file type
    allowed_extensions = {'.mp4', '.avi', '.mov', '.webm'}
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type: {file_ext}. Allowed: {allowed_extensions}"
        )
    
    # Save uploaded file to temporary location
    temp_file = None
    try:
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=file_ext)
        
        # Write uploaded content
        content = await file.read()
        temp_file.write(content)
        temp_file.close()
        
        # Get inference instance
        inference = get_inference()
        
        # Make prediction
        result = inference.predict_video(temp_file.name)
        
        return result
        
    except Exception as e:
        logger.error(f"Prediction failed: {e}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")
    
    finally:
        # Clean up temporary file
        if temp_file:
            try:
                os.unlink(temp_file.name)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file: {e}")

@router.post("/predict_batch")
async def predict_batch(files: list[UploadFile] = File(...)):
    """
    Predict multiple videos in batch.
    
    Args:
        files: List of video files
        
    Returns:
        List of prediction results
    """
    if not files:
        raise HTTPException(status_code=400, detail="No files provided")
    
    if len(files) > 10:  # Limit batch size
        raise HTTPException(status_code=400, detail="Maximum 10 files per batch")
    
    results = []
    temp_files = []
    
    try:
        # Get inference instance
        inference = get_inference()
        
        for file in files:
            if not file.filename:
                results.append({
                    'filename': 'unknown',
                    'success': False,
                    'error': 'No filename provided'
                })
                continue
            
            # Validate file type
            allowed_extensions = {'.mp4', '.avi', '.mov', '.webm'}
            file_ext = Path(file.filename).suffix.lower()
            if file_ext not in allowed_extensions:
                results.append({
                    'filename': file.filename,
                    'success': False,
                    'error': f'Unsupported file type: {file_ext}'
                })
                continue
            
            # Save to temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=file_ext)
            content = await file.read()
            temp_file.write(content)
            temp_file.close()
            temp_files.append(temp_file.name)
            
            # Make prediction
            try:
                result = inference.predict_video(temp_file.name)
                result['filename'] = file.filename
                results.append(result)
            except Exception as e:
                results.append({
                    'filename': file.filename,
                    'success': False,
                    'error': str(e)
                })
        
        return {
            'batch_size': len(files),
            'results': results,
            'summary': {
                'successful': sum(1 for r in results if r.get('success', False)),
                'failed': sum(1 for r in results if not r.get('success', False))
            }
        }
        
    except Exception as e:
        logger.error(f"Batch prediction failed: {e}")
        raise HTTPException(status_code=500, detail=f"Batch prediction failed: {str(e)}")
    
    finally:
        # Clean up temporary files
        for temp_file_path in temp_files:
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file: {e}")

@router.get("/words")
async def get_supported_words():
    """Get list of supported words."""
    try:
        inference = get_inference()
        model_info = inference.get_model_info()
        return {
            'words': model_info['words'],
            'num_classes': model_info['num_classes'],
            'confidence_thresholds': model_info['confidence_thresholds']
        }
    except Exception as e:
        logger.error(f"Failed to get words: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats")
async def get_prediction_stats():
    """Get prediction statistics (placeholder for future implementation)."""
    return {
        'message': 'Statistics endpoint not yet implemented',
        'available_endpoints': [
            '/lipnet5/health',
            '/lipnet5/info', 
            '/lipnet5/predict',
            '/lipnet5/predict_batch',
            '/lipnet5/words'
        ]
    }
