"""
LipNet 5-Word Classification Module

This module provides a LipNet-based classifier for 5 ICU anchor words:
doctor, glasses, help, phone, pillow

The module integrates with the existing backend API and provides
confidence-based predictions for clinical communication.
"""

__version__ = "1.0.0"
__author__ = "ICU-Lipreading MVP Team"

from .infer import LipNet5WordInference

__all__ = [
    "LipNet5WordInference"
]
