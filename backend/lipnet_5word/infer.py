"""
LipNet 5-Word Inference Module

Provides inference capabilities for the 5-word ICU anchor classification task.
"""

import os
import torch
import torch.nn as nn
import cv2
import numpy as np
from pathlib import Path
from typing import Dict, List, Union, Optional
import time
import logging

logger = logging.getLogger(__name__)

class Simple3DCNN(nn.Module):
    """Simple 3D CNN for 5-word classification (matches training model)"""
    
    def __init__(self, num_classes=5, dropout=0.5):
        super(Simple3DCNN, self).__init__()
        
        # 3D Convolutional layers
        self.conv3d_1 = nn.Conv3d(1, 32, kernel_size=(3, 7, 7), stride=(1, 2, 2), padding=(1, 3, 3))
        self.bn3d_1 = nn.BatchNorm3d(32)
        self.pool3d_1 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        
        self.conv3d_2 = nn.Conv3d(32, 64, kernel_size=(3, 5, 5), stride=(1, 1, 1), padding=(1, 2, 2))
        self.bn3d_2 = nn.BatchNorm3d(64)
        self.pool3d_2 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        
        self.conv3d_3 = nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))
        self.bn3d_3 = nn.BatchNorm3d(128)
        self.pool3d_3 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        
        # Global average pooling
        self.global_avg_pool = nn.AdaptiveAvgPool3d((1, 1, 1))
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(128, 64),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            nn.Linear(64, num_classes)
        )
    
    def forward(self, x):
        # Input: (batch, 1, T, H, W)
        
        # 3D CNN feature extraction
        x = torch.relu(self.bn3d_1(self.conv3d_1(x)))
        x = self.pool3d_1(x)
        
        x = torch.relu(self.bn3d_2(self.conv3d_2(x)))
        x = self.pool3d_2(x)
        
        x = torch.relu(self.bn3d_3(self.conv3d_3(x)))
        x = self.pool3d_3(x)
        
        # Global average pooling
        x = self.global_avg_pool(x)
        x = x.view(x.size(0), -1)  # Flatten
        
        # Classification
        x = self.classifier(x)
        
        return x

class LipNet5WordInference:
    """Inference engine for 5-word LipNet classification"""
    
    def __init__(self, model_path: str = "artifacts/vsr_fasthead_v1/lipnet_5word_best.pth", device: Optional[str] = None):
        self.model_path = Path(model_path)
        self.device = torch.device(device if device else ('cuda' if torch.cuda.is_available() else 'cpu'))
        
        # 5 ICU anchor words
        self.words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
        self.num_classes = len(self.words)
        
        # Confidence thresholds (from user requirements)
        self.high_confidence_threshold = 0.80  # Accept immediately
        self.medium_confidence_threshold = 0.50  # Show alternatives
        
        # Load model
        self.model = self._load_model()
        
        logger.info(f"LipNet 5-Word inference initialized on {self.device}")
        logger.info(f"Model loaded from: {self.model_path}")
        logger.info(f"Words: {self.words}")
    
    def _load_model(self) -> nn.Module:
        """Load the trained model"""
        if not self.model_path.exists():
            raise FileNotFoundError(f"Model file not found: {self.model_path}")
        
        try:
            # Load checkpoint
            checkpoint = torch.load(self.model_path, map_location=self.device)
            
            # Create model
            model = Simple3DCNN(num_classes=self.num_classes, dropout=0.5)
            
            # Load state dict
            model.load_state_dict(checkpoint['model_state_dict'])
            model.to(self.device)
            model.eval()
            
            logger.info(f"Model loaded successfully. Accuracy: {checkpoint.get('accuracy', 'unknown'):.2f}%")
            
            return model
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def preprocess_video(self, video_path: Union[str, Path]) -> torch.Tensor:
        """Preprocess video for inference (matches training preprocessing)"""
        video_path = Path(video_path)
        
        if not video_path.exists():
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        try:
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                raise ValueError(f"Cannot open video: {video_path}")
            
            # Get video properties
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # Target parameters (match training)
            target_frames = 64
            target_size = (112, 112)
            
            # Sample frames evenly
            if frame_count <= target_frames:
                frame_indices = list(range(frame_count))
                while len(frame_indices) < target_frames:
                    frame_indices.append(frame_count - 1)
            else:
                frame_indices = np.linspace(0, frame_count - 1, target_frames, dtype=int)
            
            frames = []
            for frame_idx in frame_indices:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                
                if not ret:
                    if frames:
                        frame = frames[-1].copy()
                    else:
                        continue
                
                # Convert to grayscale
                if len(frame.shape) == 3:
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                # Resize to target size
                frame = cv2.resize(frame, target_size, interpolation=cv2.INTER_AREA)
                
                # Normalize to [0, 1]
                frame = frame.astype(np.float32) / 255.0
                
                frames.append(frame)
            
            cap.release()
            
            if len(frames) != target_frames:
                raise ValueError(f"Expected {target_frames} frames, got {len(frames)}")
            
            # Stack frames into tensor: (num_frames, height, width)
            video_tensor = np.stack(frames, axis=0)
            
            # Apply z-score normalization
            mean = np.mean(video_tensor)
            std = np.std(video_tensor)
            if std > 0:
                video_tensor = (video_tensor - mean) / std
            
            # Convert to torch tensor and add batch + channel dimensions: (1, 1, T, H, W)
            video_tensor = torch.from_numpy(video_tensor).unsqueeze(0).unsqueeze(0)
            
            return video_tensor.to(self.device)
            
        except Exception as e:
            logger.error(f"Error preprocessing video {video_path}: {e}")
            raise
    
    def predict_raw(self, video_tensor: torch.Tensor) -> torch.Tensor:
        """Get raw model predictions"""
        with torch.no_grad():
            logits = self.model(video_tensor)
            probabilities = torch.softmax(logits, dim=1)
            return probabilities
    
    def predict_video(self, video_path: Union[str, Path]) -> Dict:
        """
        Predict word from video file
        
        Args:
            video_path: Path to video file
            
        Returns:
            Dictionary with prediction results
        """
        start_time = time.time()
        
        try:
            # Preprocess video
            video_tensor = self.preprocess_video(video_path)
            
            # Get predictions
            probabilities = self.predict_raw(video_tensor)
            probs = probabilities.cpu().numpy()[0]  # Remove batch dimension
            
            # Get top predictions
            top_indices = np.argsort(probs)[::-1]
            
            # Primary prediction
            top_idx = top_indices[0]
            top_word = self.words[top_idx]
            top_confidence = float(probs[top_idx])
            
            # Secondary predictions
            alternatives = []
            for i in range(1, min(3, len(self.words))):  # Top 2 alternatives
                idx = top_indices[i]
                word = self.words[idx]
                confidence = float(probs[idx])
                alternatives.append({
                    'word': word,
                    'confidence': confidence
                })
            
            # Determine action based on confidence
            if top_confidence >= self.high_confidence_threshold:
                action = 'accept'
                message = f"High confidence prediction: {top_word}"
            elif top_confidence >= self.medium_confidence_threshold:
                action = 'show_alternatives'
                message = f"Medium confidence. Consider alternatives."
            else:
                action = 'retry'
                message = f"Low confidence. Please try again."
            
            processing_time = time.time() - start_time
            
            result = {
                'success': True,
                'word': top_word,
                'confidence': top_confidence,
                'action': action,
                'message': message,
                'alternatives': alternatives,
                'all_predictions': [
                    {'word': self.words[idx], 'confidence': float(probs[idx])}
                    for idx in top_indices
                ],
                'processing_time': processing_time,
                'model_info': {
                    'model_type': 'LipNet_5Word',
                    'num_classes': self.num_classes,
                    'words': self.words
                }
            }
            
            logger.info(f"Prediction: {top_word} (confidence: {top_confidence:.3f}, action: {action})")
            
            return result
            
        except Exception as e:
            logger.error(f"Prediction failed for {video_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'word': None,
                'confidence': 0.0,
                'action': 'error',
                'message': f'Prediction failed: {str(e)}',
                'alternatives': [],
                'processing_time': time.time() - start_time
            }
    
    def get_model_info(self) -> Dict:
        """Get model information"""
        return {
            'model_type': 'LipNet_5Word',
            'model_path': str(self.model_path),
            'device': str(self.device),
            'num_classes': self.num_classes,
            'words': self.words,
            'confidence_thresholds': {
                'high': self.high_confidence_threshold,
                'medium': self.medium_confidence_threshold
            },
            'model_loaded': self.model is not None
        }

# Global inference instance (singleton pattern)
_inference_instance: Optional[LipNet5WordInference] = None

def get_inference(model_path: str = "artifacts/vsr_fasthead_v1/lipnet_5word_best.pth", 
                 device: Optional[str] = None) -> LipNet5WordInference:
    """Get or create LipNet 5-word inference instance"""
    global _inference_instance
    
    if _inference_instance is None:
        _inference_instance = LipNet5WordInference(model_path, device)
    
    return _inference_instance
