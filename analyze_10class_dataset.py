#!/usr/bin/env python3
"""
10-Class Dataset Analysis and Demographics-Based Splitting
=========================================================

Analyzes the 'classifier training 2.9.25' dataset structure, extracts demographic 
information from filenames, and creates speaker-disjoint train/val/test splits.

Target splits: 60% train / 25% val / 15% test
Requirements:
- Zero speaker overlap between splits
- Minimum 30 unique demographic combinations across all classes
- Balanced representation across demographics

Usage:
    python analyze_10class_dataset.py
"""

import os
import json
import pandas as pd
from pathlib import Path
from collections import defaultdict, Counter
import re
from typing import Dict, List, Tuple, Set
import random
import numpy as np

# Set random seeds for reproducibility
random.seed(42)
np.random.seed(42)

class DatasetAnalyzer:
    def __init__(self, dataset_path: str):
        self.dataset_path = Path(dataset_path)
        self.classes = [
            "doctor", "glasses", "help", "i_m_hot", "i_need_to_move",
            "my_back_hurts", "my_chest_hurts", "my_mouth_is_dry", "phone", "pillow"
        ]
        self.demographics_data = []
        self.speaker_demographics = {}
        
    def extract_demographics_from_filename(self, filename: str) -> Dict:
        """Extract demographic information from filename"""
        # Pattern: word__useruser01__age__gender__ethnicity__timestamp.webm
        parts = filename.replace('.webm', '').split('__')
        
        if len(parts) >= 5:
            return {
                'word': parts[0],
                'speaker_id': parts[1],
                'age_group': parts[2],
                'gender': parts[3],
                'ethnicity': parts[4],
                'timestamp': parts[5] if len(parts) > 5 else 'unknown',
                'demographic_key': f"{parts[2]}_{parts[3]}_{parts[4]}"
            }
        else:
            print(f"Warning: Unexpected filename format: {filename}")
            return None
    
    def analyze_dataset_structure(self) -> Dict:
        """Analyze complete dataset structure and demographics"""
        print("🔍 Analyzing 10-class dataset structure...")
        
        analysis = {
            'total_videos': 0,
            'classes': {},
            'demographics': defaultdict(int),
            'speakers': set(),
            'demographic_combinations': set(),
            'age_groups': defaultdict(int),
            'genders': defaultdict(int),
            'ethnicities': defaultdict(int)
        }
        
        for class_name in self.classes:
            class_path = self.dataset_path / class_name / "original"
            if not class_path.exists():
                print(f"Warning: {class_path} does not exist")
                continue
                
            class_videos = []
            video_files = list(class_path.glob("*.webm"))
            
            print(f"📁 Analyzing class '{class_name}': {len(video_files)} videos")
            
            for video_file in video_files:
                demo_info = self.extract_demographics_from_filename(video_file.name)
                if demo_info:
                    class_videos.append({
                        'filename': video_file.name,
                        'path': str(video_file),
                        **demo_info
                    })
                    
                    # Update analysis
                    analysis['speakers'].add(demo_info['speaker_id'])
                    analysis['demographic_combinations'].add(demo_info['demographic_key'])
                    analysis['demographics'][demo_info['demographic_key']] += 1
                    analysis['age_groups'][demo_info['age_group']] += 1
                    analysis['genders'][demo_info['gender']] += 1
                    analysis['ethnicities'][demo_info['ethnicity']] += 1
                    
                    # Store speaker demographics
                    self.speaker_demographics[demo_info['speaker_id']] = demo_info['demographic_key']
                    
                    self.demographics_data.append({
                        'class': class_name,
                        'filename': video_file.name,
                        'path': str(video_file),
                        **demo_info
                    })
            
            analysis['classes'][class_name] = {
                'count': len(class_videos),
                'videos': class_videos
            }
            analysis['total_videos'] += len(class_videos)
        
        # Convert sets to lists for JSON serialization
        analysis['speakers'] = list(analysis['speakers'])
        analysis['demographic_combinations'] = list(analysis['demographic_combinations'])
        analysis['demographics'] = dict(analysis['demographics'])
        analysis['age_groups'] = dict(analysis['age_groups'])
        analysis['genders'] = dict(analysis['genders'])
        analysis['ethnicities'] = dict(analysis['ethnicities'])
        
        return analysis
    
    def create_speaker_disjoint_splits(self, analysis: Dict) -> Dict:
        """Create speaker-disjoint train/val/test splits using demographics as speaker identifiers"""
        print("\n🎯 Creating speaker-disjoint splits...")
        print("📝 Note: Using demographic combinations as unique speaker identifiers")

        # Get unique demographic combinations (treating each as a unique speaker)
        unique_demographics = list(set(item['demographic_key'] for item in self.demographics_data))
        print(f"📊 Total unique demographic speakers: {len(unique_demographics)}")

        # Show demographic distribution
        from collections import Counter
        demo_counts = Counter(item['demographic_key'] for item in self.demographics_data)
        print(f"📊 Demographic speaker distribution:")
        for demo, count in sorted(demo_counts.items()):
            print(f"   {demo}: {count} videos")

        # Shuffle demographics for random assignment
        random.shuffle(unique_demographics)

        # Split demographics maintaining target ratios
        n_demographics = len(unique_demographics)
        n_train = max(1, int(n_demographics * 0.60))  # At least 1 for train
        n_val = max(1, int(n_demographics * 0.25)) if n_demographics > 2 else 0
        n_test = n_demographics - n_train - n_val

        train_demographics = set(unique_demographics[:n_train])
        val_demographics = set(unique_demographics[n_train:n_train + n_val]) if n_val > 0 else set()
        test_demographics = set(unique_demographics[n_train + n_val:]) if n_test > 0 else set()

        print(f"📊 Demographic speaker distribution:")
        print(f"   Train: {len(train_demographics)} demographics ({len(train_demographics)/n_demographics*100:.1f}%)")
        print(f"   Val: {len(val_demographics)} demographics ({len(val_demographics)/n_demographics*100:.1f}%)")
        print(f"   Test: {len(test_demographics)} demographics ({len(test_demographics)/n_demographics*100:.1f}%)")

        # Create video splits based on demographic assignments
        splits = {'train': [], 'val': [], 'test': []}

        for item in self.demographics_data:
            demo_key = item['demographic_key']
            if demo_key in train_demographics:
                splits['train'].append(item)
            elif demo_key in val_demographics:
                splits['val'].append(item)
            elif demo_key in test_demographics:
                splits['test'].append(item)

        # Verify no demographic overlap between splits
        train_demos = set(item['demographic_key'] for item in splits['train'])
        val_demos = set(item['demographic_key'] for item in splits['val'])
        test_demos = set(item['demographic_key'] for item in splits['test'])

        assert len(train_demos & val_demos) == 0, "Demographic overlap between train and val!"
        assert len(train_demos & test_demos) == 0, "Demographic overlap between train and test!"
        assert len(val_demos & test_demos) == 0, "Demographic overlap between val and test!"

        print(f"✅ Zero demographic speaker overlap verified!")

        return {
            'train': splits['train'],
            'val': splits['val'],
            'test': splits['test'],
            'train_demographics': list(train_demos),
            'val_demographics': list(val_demos),
            'test_demographics': list(test_demos)
        }
    
    def validate_splits(self, splits: Dict) -> Dict:
        """Validate split quality and demographic distribution"""
        print("\n🔍 Validating split quality...")

        validation = {}

        for split_name, split_data in [('train', splits['train']), ('val', splits['val']), ('test', splits['test'])]:
            if not split_data:
                validation[split_name] = {
                    'total_videos': 0,
                    'class_distribution': {},
                    'demographic_distribution': {},
                    'unique_demographics': 0,
                    'demographic_speakers': 0
                }
                continue

            # Class distribution
            class_counts = Counter(item['class'] for item in split_data)

            # Demographic distribution
            demo_counts = Counter(item['demographic_key'] for item in split_data)

            validation[split_name] = {
                'total_videos': len(split_data),
                'class_distribution': dict(class_counts),
                'demographic_distribution': dict(demo_counts),
                'unique_demographics': len(demo_counts),
                'demographic_speakers': len(set(item['demographic_key'] for item in split_data))
            }

            print(f"📊 {split_name.upper()} split:")
            print(f"   Videos: {len(split_data)}")
            if class_counts:
                print(f"   Classes: {len(class_counts)} (min: {min(class_counts.values())}, max: {max(class_counts.values())})")
            print(f"   Demographics: {len(demo_counts)}")
            print(f"   Demographic speakers: {validation[split_name]['demographic_speakers']}")

        # Check minimum demographic requirement
        total_demographics = len(set(item['demographic_key'] for item in
                                   splits['train'] + splits['val'] + splits['test']))

        print(f"\n🎯 Quality Metrics:")
        print(f"   Total unique demographics: {total_demographics}")
        print(f"   Minimum requirement (30): {'✅ PASS' if total_demographics >= 30 else '❌ FAIL'}")

        return validation

def main():
    """Main execution function"""
    print("🚀 10-Class Lipreading Dataset Analysis")
    print("=" * 50)
    
    # Initialize analyzer
    dataset_path = "data/classifier training 2.9.25"
    analyzer = DatasetAnalyzer(dataset_path)
    
    # Analyze dataset structure
    analysis = analyzer.analyze_dataset_structure()
    
    # Create speaker-disjoint splits
    splits = analyzer.create_speaker_disjoint_splits(analysis)
    
    # Validate splits
    validation = analyzer.validate_splits(splits)
    
    # Save results
    results = {
        'dataset_analysis': analysis,
        'splits': splits,
        'validation': validation,
        'metadata': {
            'dataset_path': dataset_path,
            'total_classes': len(analyzer.classes),
            'classes': analyzer.classes,
            'split_ratios': {'train': 0.60, 'val': 0.25, 'test': 0.15}
        }
    }
    
    output_file = "10class_dataset_analysis.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: {output_file}")
    print("\n🎉 Dataset analysis complete!")
    
    return results

if __name__ == "__main__":
    results = main()
