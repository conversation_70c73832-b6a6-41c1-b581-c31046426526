#!/usr/bin/env python3
"""
R(2+1)D-18 Transfer Learning Model for 10-Class Lipreading
=========================================================

Implements R(2+1)D-18 model with Kinetics-400 pretrained weights for 10-class
lipreading classification. Features:

- Base model: torchvision R(2+1)D-18 with Kinetics-400 pretrained weights
- Feature extraction: Remove final classifier → extract 512-dim features
- Input adaptation: Convert grayscale to RGB via tensor.repeat(1, 3, 1, 1, 1)
- Normalization: Use Kinetics-400 statistics
- Input tensor shape: (batch_size, 3, 16, 112, 112) - exactly 16 frames per sequence
- Classifier head: BiGRU(input_size=512, hidden_size=256, bidirectional=True) → Linear(512, 10)

Usage:
    from r2plus1d_10class_model import R2Plus1D10ClassModel
    model = R2Plus1D10ClassModel(num_classes=10)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import models
from torchvision.models import video as video_models
from typing import Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class R2Plus1D10ClassModel(nn.Module):
    """
    R(2+1)D-18 + BiGRU model for 10-class lipreading
    
    Architecture:
    1. R(2+1)D-18 backbone (Kinetics-400 pretrained) → 512-dim features
    2. BiGRU(512 → 256, bidirectional) → 512-dim output
    3. Linear classifier (512 → 10 classes)
    """
    
    def __init__(self, num_classes: int = 10, hidden_size: int = 256, dropout: float = 0.3):
        super(R2Plus1D10ClassModel, self).__init__()
        
        self.num_classes = num_classes
        self.hidden_size = hidden_size
        self.dropout = dropout
        
        # Load pretrained R(2+1)D-18
        try:
            # Try new API first
            self.backbone = video_models.r2plus1d_18(weights='KINETICS400_V1')
            logger.info("✅ Loaded R(2+1)D-18 with KINETICS400_V1 weights")
        except (TypeError, AttributeError):
            try:
                # Fallback to older API
                self.backbone = video_models.r2plus1d_18(pretrained=True)
                logger.info("✅ Loaded R(2+1)D-18 with pretrained=True")
            except Exception as e:
                logger.warning(f"Failed to load pretrained weights: {e}")
                self.backbone = video_models.r2plus1d_18(weights=None)
                logger.info("⚠️ Loaded R(2+1)D-18 without pretrained weights")
        
        # Remove final classifier to get 512-dim features
        self.backbone.fc = nn.Identity()
        
        # BiGRU for temporal modeling
        self.bigru = nn.GRU(
            input_size=512,
            hidden_size=hidden_size,
            num_layers=1,
            batch_first=True,
            bidirectional=True,
            dropout=0.0  # No dropout in GRU since we only have 1 layer
        )
        
        # Final classifier with dropout
        self.classifier = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(hidden_size * 2, num_classes)  # *2 for bidirectional
        )
        
        # Kinetics-400 normalization statistics
        self.register_buffer('mean', torch.tensor([0.43216, 0.394666, 0.37645]))
        self.register_buffer('std', torch.tensor([0.22803, 0.22145, 0.216989]))
        
        logger.info(f"🤖 R(2+1)D-18 + BiGRU model created")
        logger.info(f"   Backbone: Kinetics-400 pretrained R(2+1)D-18")
        logger.info(f"   BiGRU hidden size: {hidden_size}")
        logger.info(f"   Output classes: {num_classes}")
        logger.info(f"   Dropout: {dropout}")
    
    def normalize_input(self, x: torch.Tensor) -> torch.Tensor:
        """
        Normalize input tensor using Kinetics-400 statistics
        
        Args:
            x: Input tensor of shape (batch, channels, frames, height, width)
            
        Returns:
            Normalized tensor
        """
        # Reshape for normalization: (B, C, T, H, W) -> (B*T, C, H, W)
        b, c, t, h, w = x.shape
        x = x.permute(0, 2, 1, 3, 4).contiguous()  # (B, T, C, H, W)
        x = x.view(b * t, c, h, w)  # (B*T, C, H, W)
        
        # Normalize
        mean = self.mean.view(1, -1, 1, 1)
        std = self.std.view(1, -1, 1, 1)
        x = (x - mean) / std
        
        # Reshape back: (B*T, C, H, W) -> (B, C, T, H, W)
        x = x.view(b, t, c, h, w)  # (B, T, C, H, W)
        x = x.permute(0, 2, 1, 3, 4).contiguous()  # (B, C, T, H, W)
        
        return x
    
    def convert_grayscale_to_rgb(self, x: torch.Tensor) -> torch.Tensor:
        """
        Convert grayscale input to RGB by repeating channels
        
        Args:
            x: Input tensor of shape (batch, 1, frames, height, width)
            
        Returns:
            RGB tensor of shape (batch, 3, frames, height, width)
        """
        if x.size(1) == 1:
            # Repeat grayscale channel 3 times for RGB
            x = x.repeat(1, 3, 1, 1, 1)
        elif x.size(1) != 3:
            raise ValueError(f"Expected 1 or 3 channels, got {x.size(1)}")
        
        return x
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass
        
        Args:
            x: Input tensor of shape (batch, channels, frames, height, width)
               Expected: (batch, 1, 16, 112, 112) or (batch, 3, 16, 112, 112)
               
        Returns:
            Logits tensor of shape (batch, num_classes)
        """
        batch_size = x.size(0)
        
        # Convert grayscale to RGB if needed
        x = self.convert_grayscale_to_rgb(x)  # (batch, 3, 16, 112, 112)
        
        # Normalize using Kinetics-400 statistics
        x = self.normalize_input(x)
        
        # Extract features with R(2+1)D backbone
        features = self.backbone(x)  # (batch, 512)
        
        # For BiGRU, we need sequence dimension
        # Since R(2+1)D processes the entire clip, we'll use the feature as a single timestep
        features = features.unsqueeze(1)  # (batch, 1, 512)
        
        # BiGRU processing
        gru_out, _ = self.bigru(features)  # (batch, 1, 512)
        
        # Take the output from the single timestep
        gru_out = gru_out.squeeze(1)  # (batch, 512)
        
        # Classification
        output = self.classifier(gru_out)  # (batch, num_classes)
        
        return output
    
    def get_feature_extractor(self) -> nn.Module:
        """
        Get the feature extraction part of the model (backbone + BiGRU)
        
        Returns:
            Feature extractor module
        """
        class FeatureExtractor(nn.Module):
            def __init__(self, parent_model):
                super().__init__()
                self.parent = parent_model
            
            def forward(self, x):
                batch_size = x.size(0)
                
                # Convert grayscale to RGB if needed
                x = self.parent.convert_grayscale_to_rgb(x)
                
                # Normalize using Kinetics-400 statistics
                x = self.parent.normalize_input(x)
                
                # Extract features with R(2+1)D backbone
                features = self.parent.backbone(x)  # (batch, 512)
                
                # BiGRU processing
                features = features.unsqueeze(1)  # (batch, 1, 512)
                gru_out, _ = self.parent.bigru(features)  # (batch, 1, 512)
                gru_out = gru_out.squeeze(1)  # (batch, 512)
                
                return gru_out
        
        return FeatureExtractor(self)
    
    def freeze_backbone(self):
        """Freeze the R(2+1)D backbone parameters"""
        for param in self.backbone.parameters():
            param.requires_grad = False
        logger.info("🔒 R(2+1)D backbone frozen")
    
    def unfreeze_backbone(self):
        """Unfreeze the R(2+1)D backbone parameters"""
        for param in self.backbone.parameters():
            param.requires_grad = True
        logger.info("🔓 R(2+1)D backbone unfrozen")
    
    def unfreeze_final_blocks(self, num_blocks: int = 1):
        """
        Unfreeze only the final blocks of the R(2+1)D backbone
        
        Args:
            num_blocks: Number of final blocks to unfreeze
        """
        # Freeze all first
        self.freeze_backbone()
        
        # Unfreeze final blocks
        if hasattr(self.backbone, 'layer4'):
            for param in self.backbone.layer4.parameters():
                param.requires_grad = True
            logger.info(f"🔓 Unfroze final R(2+1)D block (layer4)")
        
        if num_blocks > 1 and hasattr(self.backbone, 'layer3'):
            for param in self.backbone.layer3.parameters():
                param.requires_grad = True
            logger.info(f"🔓 Unfroze R(2+1)D layer3")
    
    def get_trainable_parameters(self):
        """Get trainable parameters grouped by component"""
        backbone_params = []
        bigru_params = []
        classifier_params = []
        
        for name, param in self.named_parameters():
            if param.requires_grad:
                if name.startswith('backbone'):
                    backbone_params.append(param)
                elif name.startswith('bigru'):
                    bigru_params.append(param)
                elif name.startswith('classifier'):
                    classifier_params.append(param)
        
        return {
            'backbone': backbone_params,
            'bigru': bigru_params,
            'classifier': classifier_params
        }
    
    def print_model_info(self):
        """Print detailed model information"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        trainable_groups = self.get_trainable_parameters()
        backbone_trainable = sum(p.numel() for p in trainable_groups['backbone'])
        bigru_trainable = sum(p.numel() for p in trainable_groups['bigru'])
        classifier_trainable = sum(p.numel() for p in trainable_groups['classifier'])
        
        print(f"\n📊 R(2+1)D-18 + BiGRU Model Information:")
        print(f"   Total parameters: {total_params:,}")
        print(f"   Trainable parameters: {trainable_params:,}")
        print(f"   Backbone trainable: {backbone_trainable:,}")
        print(f"   BiGRU trainable: {bigru_trainable:,}")
        print(f"   Classifier trainable: {classifier_trainable:,}")
        print(f"   Expected input shape: (batch, 1, 16, 112, 112)")
        print(f"   Output shape: (batch, {self.num_classes})")

def test_model():
    """Test the model with dummy input"""
    print("🧪 Testing R(2+1)D-18 + BiGRU model...")
    
    # Create model
    model = R2Plus1D10ClassModel(num_classes=10)
    model.print_model_info()
    
    # Test with grayscale input
    batch_size = 2
    dummy_input = torch.randn(batch_size, 1, 16, 112, 112)
    
    print(f"\n🔍 Testing with input shape: {dummy_input.shape}")
    
    # Forward pass
    with torch.no_grad():
        output = model(dummy_input)
    
    print(f"✅ Output shape: {output.shape}")
    print(f"✅ Output range: [{output.min():.3f}, {output.max():.3f}]")
    
    # Test freezing/unfreezing
    print(f"\n🔒 Testing parameter freezing...")
    model.freeze_backbone()
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"   Trainable after freezing backbone: {trainable_params:,}")
    
    model.unfreeze_final_blocks(1)
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"   Trainable after unfreezing final block: {trainable_params:,}")
    
    print(f"✅ Model test completed successfully!")

if __name__ == "__main__":
    test_model()
