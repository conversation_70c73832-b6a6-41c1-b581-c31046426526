#!/usr/bin/env python3
"""
Fix tensor shapes in processed .npy files
Transpose from [T,H,W,C] to [T,C,H,W] for PyTorch compatibility
"""

import numpy as np
import glob
import os
from pathlib import Path
from tqdm import tqdm

def fix_shapes():
    files = glob.glob("data/processed_final_full/*.npy")
    
    if not files:
        print("No .npy files found!")
        return
    
    print(f"Found {len(files)} .npy files to fix")
    
    fixed_count = 0
    error_count = 0
    
    for f in tqdm(files, desc="Fixing tensor shapes"):
        try:
            # Load array
            arr = np.load(f)
            
            # Check if needs fixing
            if len(arr.shape) == 4 and arr.shape[-1] == 3:
                # Transpose from [T,H,W,C] to [T,C,H,W]
                arr_fixed = np.transpose(arr, (0, 3, 1, 2))
                
                # Verify new shape
                expected_shape = (32, 3, 112, 112)
                if arr_fixed.shape == expected_shape:
                    # Save fixed array
                    np.save(f, arr_fixed)
                    fixed_count += 1
                else:
                    print(f"⚠️  Unexpected shape after transpose: {f} -> {arr_fixed.shape}")
                    error_count += 1
            elif len(arr.shape) == 4 and arr.shape[1] == 3:
                # Already in correct format
                pass
            else:
                print(f"⚠️  Unexpected original shape: {f} -> {arr.shape}")
                error_count += 1
                
        except Exception as e:
            print(f"❌ Error processing {f}: {e}")
            error_count += 1
    
    print(f"\n🎯 Shape fixing complete:")
    print(f"   Fixed files: {fixed_count}")
    print(f"   Errors: {error_count}")
    print(f"   Total files: {len(files)}")
    
    # Verify a few files
    print(f"\n🔍 Verification (first 3 files):")
    for f in files[:3]:
        try:
            arr = np.load(f)
            print(f"   {Path(f).name}: {arr.shape}")
        except Exception as e:
            print(f"   {Path(f).name}: ERROR - {e}")

if __name__ == "__main__":
    fix_shapes()
