#!/usr/bin/env python3
"""
Build Clean Manifest from Metadata
==================================

Creates a clean training manifest from processed videos using QC metadata,
filtering for high-quality videos (≥80% good frames) and maintaining
speaker-wise splits.
"""

import json
import re
import csv
import sys
from pathlib import Path
from collections import defaultdict, Counter
from random import shuffle, seed

def main():
    # Set random seed for reproducible splits
    seed(42)
    
    # Configuration
    OUT_DIR = Path("data/processed_final_full")
    MANIFEST = Path("data/processed_final_full/manifest_clean.csv")
    GOOD_THRESH = 0.80
    
    print(f"🔍 Scanning for QC metadata in: {OUT_DIR}")
    
    # Find videos by presence of QC metadata
    items = []
    qc_files = list(OUT_DIR.glob("*_qc_meta.json"))
    
    if not qc_files:
        print(f"❌ No QC metadata files found in {OUT_DIR}")
        print(f"   Looking for files matching pattern: *_qc_meta.json")
        return
    
    print(f"📊 Found {len(qc_files)} QC metadata files")
    
    for qc_meta_file in qc_files:
        try:
            with open(qc_meta_file) as f:
                metadata = json.load(f)
            
            # Calculate quality metrics
            centers = metadata.get("centers", [])
            sizes = metadata.get("sizes", [])
            total = metadata.get("total", len(centers))
            
            if total == 0:
                continue
            
            # Count good frames using same criteria as QC
            good = 0
            for i, ((cx, cy), size) in enumerate(zip(centers, sizes)):
                # Check if center is near (0.5, 0.5) and size is reasonable
                center_ok = abs(cx - 0.5) <= 0.2 and abs(cy - 0.5) <= 0.2
                size_ok = 0.25 <= size <= 0.8  # Using adjusted thresholds
                
                if center_ok and size_ok and size > 0:
                    good += 1
            
            good_ratio = good / total if total > 0 else 0.0
            
            # Parse filename to extract metadata
            # Expected format: word__speaker__age__gender__ethnicity__timestamp_qc_meta.json
            filename_base = qc_meta_file.stem.replace("_qc_meta", "")
            parts = filename_base.split("__")
            
            if len(parts) >= 2:
                phrase = parts[0].lower()
                user = parts[1]
                
                # Build speaker ID from available parts
                if len(parts) >= 5:
                    age = parts[2]
                    gender = parts[3]
                    ethnicity = parts[4]
                    speaker = f"{user}_{age}_{gender}_{ethnicity}"
                else:
                    speaker = user
            else:
                # Fallback parsing
                match = re.match(r"([a-zA-Z]+)", filename_base)
                phrase = match.group(1).lower() if match else "unknown"
                speaker = "unknown"
            
            # Check for corresponding processed video file
            video_file = OUT_DIR / f"{filename_base}_processed_v2.npy"
            if not video_file.exists():
                print(f"⚠️  Skipping {filename_base}: no corresponding processed video")
                continue
            
            items.append({
                "video_path": str(video_file),
                "phrase": phrase,
                "tensor_shape": "[32, 112, 112, 3]",
                "filename": filename_base,
                "speaker": speaker,
                "good_ratio": good_ratio
            })
            
        except Exception as e:
            print(f"⚠️  Error processing {qc_meta_file}: {e}")
            continue
    
    print(f"📋 Total videos found: {len(items)}")
    
    # Filter by quality threshold
    high_quality_items = [x for x in items if x["good_ratio"] >= GOOD_THRESH]
    print(f"✅ High-quality videos (≥{GOOD_THRESH:.0%}): {len(high_quality_items)}")
    
    if len(high_quality_items) == 0:
        print(f"❌ No videos meet quality threshold of {GOOD_THRESH:.0%}")
        print(f"💡 Consider lowering threshold or checking preprocessing")
        return
    
    # Group by speaker for speaker-wise splits
    by_speaker = defaultdict(list)
    for item in high_quality_items:
        by_speaker[item["speaker"]].append(item)
    
    speakers = list(by_speaker.keys())
    shuffle(speakers)
    
    print(f"👥 Unique speakers: {len(speakers)}")
    
    # Create speaker-wise splits (70/20/10)
    n = len(speakers)
    n_train = int(0.7 * n)
    n_val = int(0.2 * n)
    
    train_speakers = set(speakers[:n_train])
    val_speakers = set(speakers[n_train:n_train + n_val])
    test_speakers = set(speakers[n_train + n_val:])
    
    def get_split(speaker):
        if speaker in train_speakers:
            return "train"
        elif speaker in val_speakers:
            return "val"
        else:
            return "test"
    
    # Create manifest directory
    MANIFEST.parent.mkdir(parents=True, exist_ok=True)
    
    # Write clean manifest
    with open(MANIFEST, "w", newline="") as f:
        writer = csv.writer(f)
        writer.writerow(["video_path", "phrase", "tensor_shape", "filename", "speaker_id", "split"])
        
        for item in high_quality_items:
            split = get_split(item["speaker"])
            writer.writerow([
                item["video_path"],
                item["phrase"],
                item["tensor_shape"],
                item["filename"],
                item["speaker"],
                split
            ])
    
    # Generate report
    split_counts = Counter([get_split(item["speaker"]) for item in high_quality_items])
    class_counts = Counter([item["phrase"] for item in high_quality_items])
    
    print(f"\n🎯 Clean Manifest Created: {MANIFEST}")
    print(f"📊 Dataset Statistics:")
    print(f"   Total videos: {len(high_quality_items)}")
    print(f"   Split distribution: {dict(split_counts)}")
    print(f"   Class distribution: {dict(class_counts)}")
    
    # Per-class quality report
    print(f"\n📈 Quality Report by Class:")
    class_quality = defaultdict(list)
    for item in high_quality_items:
        class_quality[item["phrase"]].append(item["good_ratio"])
    
    for phrase in sorted(class_quality.keys()):
        ratios = class_quality[phrase]
        avg_quality = sum(ratios) / len(ratios)
        print(f"   {phrase}: {len(ratios)} videos, {avg_quality:.2f} avg quality")
    
    # Speaker distribution check
    speaker_counts = Counter([item["speaker"] for item in high_quality_items])
    print(f"\n👥 Speaker Distribution:")
    print(f"   Train speakers: {len(train_speakers)}")
    print(f"   Val speakers: {len(val_speakers)}")
    print(f"   Test speakers: {len(test_speakers)}")
    
    # Check for potential issues
    if len(train_speakers) < 3:
        print(f"⚠️  WARNING: Very few train speakers ({len(train_speakers)})")
    
    if len(val_speakers) < 1:
        print(f"⚠️  WARNING: No validation speakers")
    
    # Check class balance
    min_class_count = min(class_counts.values())
    max_class_count = max(class_counts.values())
    if max_class_count > 3 * min_class_count:
        print(f"⚠️  WARNING: Class imbalance detected (min: {min_class_count}, max: {max_class_count})")
    
    print(f"\n✅ Ready for training with {len(high_quality_items)} high-quality videos!")

if __name__ == "__main__":
    main()
