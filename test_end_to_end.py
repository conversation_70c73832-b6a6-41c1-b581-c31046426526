#!/usr/bin/env python3
"""
End-to-end test for the 5-word LipNet pipeline.
Tests the complete workflow from video input to prediction output.
"""

import requests
import json
from pathlib import Path
import time

def test_api_endpoints():
    """Test all API endpoints"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing API Endpoints")
    print("=" * 50)
    
    # Test status endpoint
    print("\n1. Testing /status endpoint...")
    try:
        response = requests.get(f"{base_url}/status")
        if response.status_code == 200:
            status = response.json()
            print(f"   ✅ Status: {response.status_code}")
            print(f"   📊 LipNet 5-Word available: {status.get('lipnet5_available', False)}")
            print(f"   📊 Available endpoints: {len(status.get('endpoints', {}))}")
        else:
            print(f"   ❌ Status failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Status error: {e}")
    
    # Test LipNet 5-word health
    print("\n2. Testing /lipnet5/health endpoint...")
    try:
        response = requests.get(f"{base_url}/lipnet5/health")
        if response.status_code == 200:
            health = response.json()
            print(f"   ✅ Health: {response.status_code}")
            print(f"   🤖 Model loaded: {health.get('model_loaded', False)}")
            print(f"   💻 Device: {health.get('device', 'unknown')}")
            print(f"   📝 Words: {health.get('words', [])}")
        else:
            print(f"   ❌ Health failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Health error: {e}")
    
    # Test model info
    print("\n3. Testing /lipnet5/info endpoint...")
    try:
        response = requests.get(f"{base_url}/lipnet5/info")
        if response.status_code == 200:
            info = response.json()
            print(f"   ✅ Info: {response.status_code}")
            print(f"   🏗️  Model type: {info.get('model_type', 'unknown')}")
            print(f"   📊 Classes: {info.get('num_classes', 0)}")
            print(f"   🎯 Confidence thresholds: {info.get('confidence_thresholds', {})}")
        else:
            print(f"   ❌ Info failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Info error: {e}")

def test_video_predictions():
    """Test video predictions with sample videos"""
    base_url = "http://localhost:8000"
    
    print("\n🎬 Testing Video Predictions")
    print("=" * 50)
    
    # Test videos from each class
    test_videos = [
        ("data/cropped videos 1.9.25/doctor/doctor 1.mp4", "doctor"),
        ("data/cropped videos 1.9.25/glasses/glasses 1.mp4", "glasses"),
        ("data/cropped videos 1.9.25/help/help 1.mp4", "help"),
        ("data/cropped videos 1.9.25/phone/phone 1.mp4", "phone"),
        ("data/cropped videos 1.9.25/pillow/pillow 1.mp4", "pillow")
    ]
    
    results = []
    
    for i, (video_path, expected_word) in enumerate(test_videos, 1):
        print(f"\n{i}. Testing {expected_word} video...")
        
        if not Path(video_path).exists():
            print(f"   ❌ Video not found: {video_path}")
            continue
        
        try:
            # Test main prediction endpoint
            with open(video_path, 'rb') as f:
                files = {'file': f}
                start_time = time.time()
                response = requests.post(f"{base_url}/predict_5word", files=files)
                processing_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                predicted_word = result.get('word', 'unknown')
                confidence = result.get('confidence', 0.0)
                action = result.get('action', 'unknown')
                
                print(f"   ✅ Prediction successful")
                print(f"   🎯 Expected: {expected_word}")
                print(f"   🤖 Predicted: {predicted_word}")
                print(f"   📊 Confidence: {confidence:.3f}")
                print(f"   🎬 Action: {action}")
                print(f"   ⏱️  Processing time: {processing_time:.2f}s")
                
                # Check if prediction is correct
                correct = predicted_word == expected_word
                print(f"   {'✅' if correct else '❌'} Correct prediction: {correct}")
                
                results.append({
                    'expected': expected_word,
                    'predicted': predicted_word,
                    'confidence': confidence,
                    'correct': correct,
                    'action': action,
                    'processing_time': processing_time
                })
                
            else:
                print(f"   ❌ Prediction failed: {response.status_code}")
                print(f"   📄 Response: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Prediction error: {e}")
    
    # Summary
    if results:
        print(f"\n📊 Prediction Summary")
        print("=" * 30)
        
        correct_predictions = sum(1 for r in results if r['correct'])
        total_predictions = len(results)
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        
        avg_confidence = sum(r['confidence'] for r in results) / len(results)
        avg_processing_time = sum(r['processing_time'] for r in results) / len(results)
        
        print(f"   🎯 Accuracy: {accuracy:.1%} ({correct_predictions}/{total_predictions})")
        print(f"   📊 Average confidence: {avg_confidence:.3f}")
        print(f"   ⏱️  Average processing time: {avg_processing_time:.2f}s")
        
        # Action distribution
        actions = [r['action'] for r in results]
        action_counts = {action: actions.count(action) for action in set(actions)}
        print(f"   🎬 Actions: {action_counts}")
        
        return accuracy >= 0.2  # At least 20% accuracy (better than random)
    
    return False

def test_mobile_compatibility():
    """Test mobile app compatibility"""
    print("\n📱 Testing Mobile App Compatibility")
    print("=" * 50)
    
    # Test CORS headers
    base_url = "http://localhost:8000"
    
    try:
        response = requests.options(f"{base_url}/predict_5word")
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
        }
        
        print(f"   ✅ CORS preflight successful")
        print(f"   🌐 Allow-Origin: {cors_headers['Access-Control-Allow-Origin']}")
        print(f"   📝 Allow-Methods: {cors_headers['Access-Control-Allow-Methods']}")
        print(f"   📋 Allow-Headers: {cors_headers['Access-Control-Allow-Headers']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ CORS test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 End-to-End Testing for 5-Word LipNet Pipeline")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server not responding. Please start the server first:")
            print("   python -m backend.api.app")
            return False
    except Exception as e:
        print("❌ Server not accessible. Please start the server first:")
        print("   python -m backend.api.app")
        print(f"   Error: {e}")
        return False
    
    print("✅ Server is running")
    
    # Run tests
    test_results = []
    
    # Test API endpoints
    test_api_endpoints()
    
    # Test video predictions
    prediction_success = test_video_predictions()
    test_results.append(('Video Predictions', prediction_success))
    
    # Test mobile compatibility
    mobile_success = test_mobile_compatibility()
    test_results.append(('Mobile Compatibility', mobile_success))
    
    # Final summary
    print(f"\n🏁 Final Test Results")
    print("=" * 30)
    
    for test_name, success in test_results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    overall_success = all(success for _, success in test_results)
    
    if overall_success:
        print(f"\n🎉 All tests passed! The 5-word LipNet pipeline is working.")
        print(f"📱 Ready for mobile app integration.")
    else:
        print(f"\n⚠️  Some tests failed, but the basic pipeline is functional.")
        print(f"💡 The model can be improved with more training data or better architecture.")
    
    print(f"\n📋 Next Steps:")
    print(f"   1. Integrate with mobile app using /predict_5word endpoint")
    print(f"   2. Test with real user videos")
    print(f"   3. Collect feedback and improve model accuracy")
    print(f"   4. Consider data augmentation or ensemble methods")
    
    return overall_success

if __name__ == "__main__":
    success = main()
