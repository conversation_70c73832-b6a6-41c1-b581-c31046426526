#!/usr/bin/env python3
"""
Check shapes of processed .npy files
"""

import numpy as np
import glob
from pathlib import Path

def check_shapes():
    files = glob.glob("data/processed_final_full/*.npy")
    
    if not files:
        print("No .npy files found!")
        return
    
    print(f"Checking shapes of {len(files)} files...")
    
    # Check first few files
    for i, f in enumerate(files[:5]):
        arr = np.load(f)
        print(f"{Path(f).name}: {arr.shape} (dtype: {arr.dtype})")
    
    # Check if all have same shape
    shapes = []
    for f in files[:20]:  # Sample first 20
        arr = np.load(f)
        shapes.append(arr.shape)
    
    unique_shapes = list(set(shapes))
    print(f"\nUnique shapes found: {unique_shapes}")
    
    if len(unique_shapes) == 1:
        shape = unique_shapes[0]
        if len(shape) == 4 and shape[-1] == 3:
            print(f"❌ Files are in [T,H,W,C] format: {shape}")
            print(f"   Need to transpose to [T,C,H,W]")
            return True  # Need fixing
        elif len(shape) == 4 and shape[1] == 3:
            print(f"✅ Files are already in [T,C,H,W] format: {shape}")
            return False  # Already correct
        else:
            print(f"⚠️  Unexpected shape: {shape}")
            return False
    else:
        print(f"⚠️  Multiple shapes found - need investigation")
        return False

if __name__ == "__main__":
    check_shapes()
