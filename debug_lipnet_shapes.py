#!/usr/bin/env python3
"""
Debug tensor shapes in the LipNet model to fix dimension mismatch.
"""

import torch
import torch.nn as nn

def debug_conv_layers():
    """Debug the 3D CNN layers to understand output dimensions"""
    print("🔍 Debugging 3D CNN layer dimensions...")
    
    # Input tensor: (batch=1, channels=1, time=64, height=112, width=112)
    x = torch.randn(1, 1, 64, 112, 112)
    print(f"Input shape: {x.shape}")
    
    # Layer 1: Conv3d + BatchNorm + MaxPool
    conv3d_1 = nn.Conv3d(1, 32, kernel_size=(3, 7, 7), stride=(1, 2, 2), padding=(1, 3, 3))
    bn3d_1 = nn.BatchNorm3d(32)
    pool3d_1 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
    
    x = torch.relu(bn3d_1(conv3d_1(x)))
    print(f"After conv3d_1 + bn + relu: {x.shape}")
    x = pool3d_1(x)
    print(f"After pool3d_1: {x.shape}")
    
    # Layer 2: Conv3d + BatchNorm + Max<PERSON>ool
    conv3d_2 = nn.Conv3d(32, 64, kernel_size=(3, 5, 5), stride=(1, 1, 1), padding=(1, 2, 2))
    bn3d_2 = nn.BatchNorm3d(64)
    pool3d_2 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
    
    x = torch.relu(bn3d_2(conv3d_2(x)))
    print(f"After conv3d_2 + bn + relu: {x.shape}")
    x = pool3d_2(x)
    print(f"After pool3d_2: {x.shape}")
    
    # Layer 3: Conv3d + BatchNorm + MaxPool
    conv3d_3 = nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))
    bn3d_3 = nn.BatchNorm3d(128)
    pool3d_3 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
    
    x = torch.relu(bn3d_3(conv3d_3(x)))
    print(f"After conv3d_3 + bn + relu: {x.shape}")
    x = pool3d_3(x)
    print(f"After pool3d_3: {x.shape}")
    
    # Reshape for LSTM
    batch_size = x.size(0)
    print(f"\nReshaping for LSTM:")
    print(f"Original shape: {x.shape}")
    
    # Permute to (batch, time, channels, height, width)
    x = x.permute(0, 2, 1, 3, 4)
    print(f"After permute (0,2,1,3,4): {x.shape}")
    
    # Flatten spatial dimensions
    x = x.contiguous().view(batch_size, x.size(1), -1)
    print(f"After flatten: {x.shape}")
    
    # Calculate LSTM input size
    lstm_input_size = x.size(2)
    print(f"LSTM input size should be: {lstm_input_size}")
    
    return lstm_input_size

def test_corrected_model():
    """Test the corrected model architecture"""
    print("\n🧪 Testing corrected model architecture...")
    
    # Get correct LSTM input size
    lstm_input_size = debug_conv_layers()
    
    # Create LSTM with correct input size
    lstm = nn.LSTM(
        input_size=lstm_input_size,
        hidden_size=256,
        num_layers=2,
        batch_first=True,
        dropout=0.3,
        bidirectional=True
    )
    
    # Test with sample input
    x = torch.randn(1, 1, 64, 112, 112)
    print(f"\n🔬 Testing full forward pass:")
    print(f"Input: {x.shape}")
    
    # 3D CNN layers (simplified)
    conv3d_1 = nn.Conv3d(1, 32, kernel_size=(3, 7, 7), stride=(1, 2, 2), padding=(1, 3, 3))
    pool3d_1 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
    conv3d_2 = nn.Conv3d(32, 64, kernel_size=(3, 5, 5), stride=(1, 1, 1), padding=(1, 2, 2))
    pool3d_2 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
    conv3d_3 = nn.Conv3d(64, 128, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))
    pool3d_3 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
    
    # Forward pass
    x = pool3d_1(torch.relu(conv3d_1(x)))
    x = pool3d_2(torch.relu(conv3d_2(x)))
    x = pool3d_3(torch.relu(conv3d_3(x)))
    
    print(f"After 3D CNN: {x.shape}")
    
    # Reshape for LSTM
    batch_size = x.size(0)
    x = x.permute(0, 2, 1, 3, 4)
    x = x.contiguous().view(batch_size, x.size(1), -1)
    
    print(f"LSTM input: {x.shape}")
    
    # LSTM
    lstm_out, _ = lstm(x)
    print(f"LSTM output: {lstm_out.shape}")
    
    # Attention
    attention = nn.MultiheadAttention(
        embed_dim=512,  # 256 * 2 (bidirectional)
        num_heads=8,
        dropout=0.3,
        batch_first=True
    )
    
    attn_out, _ = attention(lstm_out, lstm_out, lstm_out)
    print(f"Attention output: {attn_out.shape}")
    
    # Global average pooling
    pooled = torch.mean(attn_out, dim=1)
    print(f"Pooled output: {pooled.shape}")
    
    # Classification head
    classifier = nn.Sequential(
        nn.Dropout(0.4),
        nn.Linear(512, 256),
        nn.ReLU(inplace=True),
        nn.Dropout(0.4),
        nn.Linear(256, 128),
        nn.ReLU(inplace=True),
        nn.Dropout(0.4),
        nn.Linear(128, 5)
    )
    
    output = classifier(pooled)
    print(f"Final output: {output.shape}")
    
    print(f"\n✅ Model architecture test successful!")
    return True

if __name__ == "__main__":
    test_corrected_model()
