#!/usr/bin/env python3
"""
Aggressive data augmentation to dramatically increase dataset size.
Generate multiple augmented versions of each video to create a much larger training set.
"""

import os
import torch
import torch.nn.functional as F
import numpy as np
import pandas as pd
from pathlib import Path
import json
from tqdm import tqdm
import cv2
import random
from scipy import ndimage
import time

class AggressiveVideoAugmenter:
    """Aggressive video augmentation for dramatic dataset expansion"""
    
    def __init__(self, augmentations_per_video=20):
        self.augmentations_per_video = augmentations_per_video
        print(f"🚀 Aggressive Augmenter initialized:")
        print(f"   Augmentations per video: {augmentations_per_video}")
        print(f"   Expected dataset expansion: {augmentations_per_video}x")
    
    def apply_temporal_augmentations(self, video_tensor):
        """Apply temporal augmentations"""
        # video_tensor shape: (1, T, H, W)
        augmented = video_tensor.clone()
        
        # 1. Temporal jittering - reorder frames slightly
        if random.random() < 0.7:
            num_frames = augmented.size(1)
            # Create slight temporal permutation
            jitter_strength = max(1, int(num_frames * 0.1))  # 10% of frames
            for _ in range(jitter_strength):
                i = random.randint(0, num_frames - 2)
                if random.random() < 0.5:
                    # Swap adjacent frames
                    augmented[:, [i, i+1]] = augmented[:, [i+1, i]]
        
        # 2. Temporal dropout - randomly drop some frames and repeat others
        if random.random() < 0.5:
            num_frames = augmented.size(1)
            keep_ratio = 0.8 + random.random() * 0.2  # Keep 80-100% of frames
            keep_frames = int(num_frames * keep_ratio)
            
            # Randomly select frames to keep
            frame_indices = torch.randperm(num_frames)[:keep_frames]
            frame_indices = torch.sort(frame_indices)[0]
            
            # Pad back to original length by repeating frames
            while len(frame_indices) < num_frames:
                repeat_idx = random.randint(0, len(frame_indices) - 1)
                frame_indices = torch.cat([frame_indices, frame_indices[repeat_idx:repeat_idx+1]])
            
            augmented = augmented[:, frame_indices[:num_frames]]
        
        # 3. Temporal speed variation - simulate different speaking speeds
        if random.random() < 0.4:
            speed_factor = 0.8 + random.random() * 0.4  # 0.8x to 1.2x speed
            num_frames = augmented.size(1)
            new_length = int(num_frames * speed_factor)
            
            if new_length != num_frames:
                # Resample frames
                indices = torch.linspace(0, num_frames - 1, new_length)
                indices = torch.clamp(indices.long(), 0, num_frames - 1)
                
                # Pad or truncate to original length
                if new_length < num_frames:
                    # Pad by repeating last frame
                    padding = num_frames - new_length
                    last_frame = augmented[:, -1:].repeat(1, padding, 1, 1)
                    augmented = torch.cat([augmented[:, indices], last_frame], dim=1)
                else:
                    # Truncate
                    augmented = augmented[:, indices[:num_frames]]
        
        return augmented
    
    def apply_spatial_augmentations(self, video_tensor):
        """Apply spatial augmentations"""
        # video_tensor shape: (1, T, H, W)
        augmented = video_tensor.clone()
        
        # 1. Random horizontal flip
        if random.random() < 0.5:
            augmented = torch.flip(augmented, dims=[-1])
        
        # 2. Random rotation
        if random.random() < 0.6:
            angle = (random.random() - 0.5) * 30  # -15 to +15 degrees
            for t in range(augmented.size(1)):
                frame = augmented[0, t].numpy()
                rotated = ndimage.rotate(frame, angle, reshape=False, mode='nearest')
                augmented[0, t] = torch.from_numpy(rotated)
        
        # 3. Random scaling/cropping
        if random.random() < 0.7:
            scale_factor = 0.8 + random.random() * 0.4  # 0.8x to 1.2x
            h, w = augmented.shape[-2:]
            
            if scale_factor != 1.0:
                new_h, new_w = int(h * scale_factor), int(w * scale_factor)
                
                # Resize all frames
                for t in range(augmented.size(1)):
                    frame = augmented[0, t].unsqueeze(0).unsqueeze(0)  # Add batch and channel dims
                    resized = F.interpolate(frame, size=(new_h, new_w), mode='bilinear', align_corners=False)
                    
                    # Crop or pad to original size
                    if new_h >= h and new_w >= w:
                        # Crop center
                        start_h = (new_h - h) // 2
                        start_w = (new_w - w) // 2
                        augmented[0, t] = resized[0, 0, start_h:start_h+h, start_w:start_w+w]
                    else:
                        # Pad
                        pad_h = (h - new_h) // 2
                        pad_w = (w - new_w) // 2
                        padded = F.pad(resized[0, 0], (pad_w, pad_w, pad_h, pad_h), mode='reflect')
                        augmented[0, t] = padded[:h, :w]  # Ensure exact size
        
        # 4. Random translation
        if random.random() < 0.5:
            max_shift = 8  # pixels
            shift_x = random.randint(-max_shift, max_shift)
            shift_y = random.randint(-max_shift, max_shift)
            
            if shift_x != 0 or shift_y != 0:
                augmented = torch.roll(augmented, shifts=(shift_y, shift_x), dims=(-2, -1))
        
        return augmented
    
    def apply_photometric_augmentations(self, video_tensor):
        """Apply photometric augmentations"""
        # video_tensor shape: (1, T, H, W)
        augmented = video_tensor.clone()
        
        # 1. Random brightness
        if random.random() < 0.8:
            brightness_factor = 0.7 + random.random() * 0.6  # 0.7 to 1.3
            augmented = augmented * brightness_factor
        
        # 2. Random contrast
        if random.random() < 0.8:
            contrast_factor = 0.7 + random.random() * 0.6  # 0.7 to 1.3
            mean = torch.mean(augmented)
            augmented = (augmented - mean) * contrast_factor + mean
        
        # 3. Random gamma correction
        if random.random() < 0.5:
            gamma = 0.7 + random.random() * 0.6  # 0.7 to 1.3
            # Normalize to [0, 1] for gamma correction
            min_val = torch.min(augmented)
            max_val = torch.max(augmented)
            normalized = (augmented - min_val) / (max_val - min_val + 1e-8)
            gamma_corrected = torch.pow(normalized, gamma)
            augmented = gamma_corrected * (max_val - min_val) + min_val
        
        # 4. Add Gaussian noise
        if random.random() < 0.6:
            noise_std = 0.02 + random.random() * 0.03  # 0.02 to 0.05
            noise = torch.randn_like(augmented) * noise_std
            augmented = augmented + noise
        
        # 5. Random blur
        if random.random() < 0.3:
            kernel_size = random.choice([3, 5])
            sigma = 0.5 + random.random() * 1.0  # 0.5 to 1.5
            
            for t in range(augmented.size(1)):
                frame = augmented[0, t].numpy()
                blurred = ndimage.gaussian_filter(frame, sigma=sigma)
                augmented[0, t] = torch.from_numpy(blurred)
        
        # 6. Random sharpening
        if random.random() < 0.3:
            sharpen_strength = 0.1 + random.random() * 0.3  # 0.1 to 0.4
            
            for t in range(augmented.size(1)):
                frame = augmented[0, t].numpy()
                # Simple sharpening kernel
                kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]]) * sharpen_strength
                kernel[1, 1] += 1  # Add identity
                sharpened = ndimage.convolve(frame, kernel, mode='reflect')
                augmented[0, t] = torch.from_numpy(sharpened)
        
        # Clamp values to reasonable range
        augmented = torch.clamp(augmented, -4, 4)
        
        return augmented
    
    def generate_augmented_video(self, video_tensor, augmentation_id):
        """Generate a single augmented version of a video"""
        # Set random seed based on augmentation_id for reproducibility
        random.seed(augmentation_id)
        np.random.seed(augmentation_id)
        torch.manual_seed(augmentation_id)
        
        augmented = video_tensor.clone()
        
        # Apply different types of augmentations
        augmented = self.apply_temporal_augmentations(augmented)
        augmented = self.apply_spatial_augmentations(augmented)
        augmented = self.apply_photometric_augmentations(augmented)
        
        return augmented
    
    def augment_dataset(self, input_manifest_path, output_dir):
        """Augment entire dataset"""
        print(f"\n🎬 Starting aggressive dataset augmentation...")
        print(f"Input manifest: {input_manifest_path}")
        print(f"Output directory: {output_dir}")
        
        # Load manifest
        manifest_df = pd.read_csv(input_manifest_path)
        print(f"Original dataset: {len(manifest_df)} videos")
        
        # Create output directory
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Track augmented videos
        augmented_manifest = []
        successful_augmentations = 0
        total_augmentations = len(manifest_df) * self.augmentations_per_video
        
        print(f"Target augmented dataset: {total_augmentations} videos")
        
        # Process each original video
        for idx, row in tqdm(manifest_df.iterrows(), total=len(manifest_df), desc="Augmenting videos"):
            original_path = Path(row['video_path'])
            
            if not original_path.exists():
                print(f"⚠️  Original video not found: {original_path}")
                continue
            
            # Load original video
            try:
                original_tensor = torch.load(original_path)
            except Exception as e:
                print(f"⚠️  Error loading {original_path}: {e}")
                continue
            
            # Generate multiple augmented versions
            for aug_id in range(self.augmentations_per_video):
                try:
                    # Generate augmented video
                    augmented_tensor = self.generate_augmented_video(
                        original_tensor, 
                        idx * self.augmentations_per_video + aug_id
                    )
                    
                    # Create output filename
                    original_stem = original_path.stem
                    output_filename = f"{original_stem}_aug_{aug_id:03d}.pt"
                    output_path = output_dir / output_filename
                    
                    # Save augmented video
                    torch.save(augmented_tensor, output_path)
                    
                    # Add to manifest
                    augmented_manifest.append({
                        'video_path': str(output_path),
                        'original_path': str(original_path),
                        'word': row['word'],
                        'label': row['label'],
                        'split': row['split'],
                        'speaker': row['speaker'],
                        'filename': row['filename'],
                        'augmentation_id': aug_id,
                        'is_augmented': True
                    })
                    
                    successful_augmentations += 1
                    
                except Exception as e:
                    print(f"⚠️  Error augmenting {original_path} (aug {aug_id}): {e}")
        
        # Save augmented manifest
        if augmented_manifest:
            augmented_df = pd.DataFrame(augmented_manifest)
            manifest_path = output_dir / "augmented_manifest.csv"
            augmented_df.to_csv(manifest_path, index=False)
            
            print(f"\n✅ Augmentation completed!")
            print(f"   Original videos: {len(manifest_df)}")
            print(f"   Augmented videos: {successful_augmentations}")
            print(f"   Total expansion: {successful_augmentations / len(manifest_df):.1f}x")
            print(f"   Success rate: {successful_augmentations / total_augmentations:.1%}")
            print(f"   Manifest saved: {manifest_path}")
            
            # Print class distribution
            class_counts = augmented_df['word'].value_counts()
            print(f"\n📊 Augmented class distribution:")
            for word, count in class_counts.items():
                print(f"   {word}: {count} videos")
            
            return str(manifest_path)
        else:
            print("❌ No videos were successfully augmented!")
            return None

def main():
    """Main augmentation function"""
    print("🚀 Aggressive Data Augmentation Pipeline")
    print("=" * 60)
    
    # Configuration
    augmentations_per_video = 50  # Dramatically increase dataset size
    
    # Input manifest (training set only)
    input_manifest = "data/speaker_separated_processed/train_processed_manifest.csv"
    
    if not Path(input_manifest).exists():
        print(f"❌ Input manifest not found: {input_manifest}")
        print("   Run preprocess_speaker_separated.py first!")
        return False
    
    # Output directory
    output_dir = "data/speaker_separated_augmented"
    
    # Create augmenter
    augmenter = AggressiveVideoAugmenter(augmentations_per_video=augmentations_per_video)
    
    # Augment training dataset
    start_time = time.time()
    augmented_manifest_path = augmenter.augment_dataset(input_manifest, output_dir)
    processing_time = time.time() - start_time
    
    if augmented_manifest_path:
        print(f"\n🎉 SUCCESS: Dataset dramatically expanded!")
        print(f"   Processing time: {processing_time/60:.1f} minutes")
        print(f"   Augmented manifest: {augmented_manifest_path}")
        
        # Load and verify the augmented dataset
        augmented_df = pd.read_csv(augmented_manifest_path)
        original_count = len(pd.read_csv(input_manifest))
        
        print(f"\n📈 Final Statistics:")
        print(f"   Original training videos: {original_count}")
        print(f"   Augmented training videos: {len(augmented_df)}")
        print(f"   Dataset expansion: {len(augmented_df) / original_count:.1f}x")
        print(f"   Videos per class: {len(augmented_df) // 5}")
        
        print(f"\n🚀 Ready for training with dramatically expanded dataset!")
        return True
    else:
        print(f"\n❌ Augmentation failed!")
        return False

if __name__ == "__main__":
    success = main()
