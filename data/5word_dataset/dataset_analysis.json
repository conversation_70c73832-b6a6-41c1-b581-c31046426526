{"dataset_summary": {"total_videos": 100, "words": ["doctor", "glasses", "help", "phone", "pillow"], "videos_per_word": {"doctor": 20, "glasses": 20, "help": 20, "phone": 20, "pillow": 20}, "balanced": true}, "video_properties": {"doctor": {"count": 20, "avg_frame_count": 48.2, "avg_fps": 29.070929070929065, "avg_width": 1280.0, "avg_height": 720.0, "avg_duration": 1.6687504166666667, "avg_brightness": 105.76457958984375, "avg_contrast": 27.416802664886497}, "glasses": {"count": 20, "avg_frame_count": 56.15, "avg_fps": 29.224525474525468, "avg_width": 1280.0, "avg_height": 720.0, "avg_duration": 1.9196400000000005, "avg_brightness": 106.10769455295137, "avg_contrast": 26.960423462223}, "help": {"count": 20, "avg_frame_count": 54.9, "avg_fps": 29.970029970029962, "avg_width": 1280.0, "avg_height": 720.0, "avg_duration": 1.8318299999999996, "avg_brightness": 102.00988850911457, "avg_contrast": 26.560922873973688}, "phone": {"count": 20, "avg_frame_count": 51.35, "avg_fps": 29.970029970029962, "avg_width": 1280.0, "avg_height": 720.0, "avg_duration": 1.7133783333333334, "avg_brightness": 104.44634857855904, "avg_contrast": 27.02477681589192}, "pillow": {"count": 20, "avg_frame_count": 54.15, "avg_fps": 29.970029970029962, "avg_width": 1280.0, "avg_height": 720.0, "avg_duration": 1.806805, "avg_brightness": 113.00082134331596, "avg_contrast": 26.21538448707297}}, "quality_analysis": {}, "recommendations": []}