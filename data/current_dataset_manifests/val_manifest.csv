video_path,word,label,split,speaker,filename,video_num
data/VAL SET/phone 8.mp4,phone,3,val,speaker_08,phone 8.mp4,8
data/VAL SET/doctor 2.mp4,doctor,0,val,speaker_02,doctor 2.mp4,2
data/VAL SET/pillow 10.mp4,pillow,4,val,speaker_10,pillow 10.mp4,10
data/VAL SET/help 7.mp4,help,2,val,speaker_07,help 7.mp4,7
data/VAL SET/doctor 11.mp4,doctor,0,val,speaker_11,doctor 11.mp4,11
data/VAL SET/pillow 16.mp4,pillow,4,val,speaker_16,pillow 16.mp4,16
data/VAL SET/glasses 9.mp4,glasses,1,val,speaker_09,glasses 9.mp4,9
data/VAL SET/phone 15.mp4,phone,3,val,speaker_15,phone 15.mp4,15
data/VAL SET/glasses 7.mp4,glasses,1,val,speaker_07,glasses 7.mp4,7
data/VAL SET/help 9.mp4,help,2,val,speaker_09,help 9.mp4,9
data/VAL SET/glasses 17.mp4,glasses,1,val,speaker_17,glasses 17.mp4,17
