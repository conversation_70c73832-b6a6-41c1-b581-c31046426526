#!/usr/bin/env python3
"""
Massive validation set expansion using all available videos from 'top 5 dataset 30.8.25'.
This is the final attempt to achieve >80% validation accuracy by creating a much larger,
more representative validation set.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.models.video as video_models
import torchvision.transforms as transforms
import pandas as pd
import numpy as np
from pathlib import Path
import json
import cv2
import mediapipe as mp
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, classification_report
import warnings
warnings.filterwarnings('ignore')

class MassiveValidationExpander:
    """Expand validation set with ALL available videos from top 5 dataset"""
    
    def __init__(self):
        self.mp_face_detection = mp.solutions.face_detection.FaceDetection(
            model_selection=0, min_detection_confidence=0.5
        )
        self.mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        
        # Mouth landmark indices for MediaPipe
        self.MOUTH_LANDMARKS = [
            61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
            78, 191, 80, 81, 82, 13, 312, 311, 310, 415, 95, 88, 178,
            87, 14, 317, 402, 318, 324, 308
        ]
        
        print("🔧 MassiveValidationExpander initialized")
    
    def extract_mouth_roi(self, frame):
        """Extract mouth ROI using MediaPipe"""
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Try FaceMesh first
        results = self.mp_face_mesh.process(rgb_frame)
        
        if results.multi_face_landmarks:
            landmarks = results.multi_face_landmarks[0]
            h, w = frame.shape[:2]
            
            # Get mouth landmarks
            mouth_points = []
            for idx in self.MOUTH_LANDMARKS:
                x = int(landmarks.landmark[idx].x * w)
                y = int(landmarks.landmark[idx].y * h)
                mouth_points.append([x, y])
            
            mouth_points = np.array(mouth_points)
            
            # Calculate bounding box with padding
            x_min, y_min = mouth_points.min(axis=0)
            x_max, y_max = mouth_points.max(axis=0)
            
            # Add padding and make square
            padding = 20
            x_min = max(0, x_min - padding)
            y_min = max(0, y_min - padding)
            x_max = min(w, x_max + padding)
            y_max = min(h, y_max + padding)
            
            # Make square crop
            center_x = (x_min + x_max) // 2
            center_y = (y_min + y_max) // 2
            size = max(x_max - x_min, y_max - y_min)
            half_size = size // 2
            
            x_min = max(0, center_x - half_size)
            y_min = max(0, center_y - half_size)
            x_max = min(w, center_x + half_size)
            y_max = min(h, center_y + half_size)
            
            return frame[y_min:y_max, x_min:x_max]
        
        # Fallback to face detection
        results = self.mp_face_detection.process(rgb_frame)
        
        if results.detections:
            detection = results.detections[0]
            bbox = detection.location_data.relative_bounding_box
            h, w = frame.shape[:2]
            
            x = int(bbox.xmin * w)
            y = int(bbox.ymin * h + bbox.height * h * 0.6)
            width = int(bbox.width * w)
            height = int(bbox.height * h * 0.4)
            
            x = max(0, x)
            y = max(0, y)
            width = min(width, w - x)
            height = min(height, h - y)
            
            return frame[y:y+height, x:x+width]
        
        return None
    
    def preprocess_video(self, video_path, target_frames=64):
        """Preprocess video with MediaPipe mouth extraction"""
        try:
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                return None
            
            frames = []
            frame_count = 0
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            if total_frames < 10:  # Skip very short videos
                cap.release()
                return None
            
            # Sample frames evenly
            frame_indices = np.linspace(0, total_frames - 1, target_frames, dtype=int)
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_count in frame_indices:
                    # Extract mouth ROI
                    mouth_roi = self.extract_mouth_roi(frame)
                    
                    if mouth_roi is not None and mouth_roi.size > 0:
                        # Resize to 112x112 and convert to grayscale
                        mouth_roi = cv2.resize(mouth_roi, (112, 112))
                        mouth_roi = cv2.cvtColor(mouth_roi, cv2.COLOR_BGR2GRAY)
                        
                        # Apply CLAHE for contrast enhancement
                        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                        mouth_roi = clahe.apply(mouth_roi)
                        
                        # Normalize to [-1, 1]
                        mouth_roi = mouth_roi.astype(np.float32) / 127.5 - 1.0
                        
                        frames.append(mouth_roi)
                
                frame_count += 1
            
            cap.release()
            
            if len(frames) < target_frames // 2:  # Need at least half the frames
                return None
            
            # Pad or truncate to exact target_frames
            if len(frames) < target_frames:
                # Repeat last frame
                while len(frames) < target_frames:
                    frames.append(frames[-1])
            elif len(frames) > target_frames:
                frames = frames[:target_frames]
            
            # Convert to tensor: (1, T, H, W)
            video_tensor = torch.tensor(np.array(frames), dtype=torch.float32)
            video_tensor = video_tensor.unsqueeze(0)  # Add channel dimension
            
            return video_tensor
            
        except Exception as e:
            print(f"Error processing {video_path}: {e}")
            return None
    
    def create_massive_validation_set(self, max_per_class=100):
        """Create massive validation set with up to 100 videos per class"""
        print("🚀 Creating Massive Validation Set")
        print("=" * 70)
        
        # Get list of all videos from top 5 dataset
        top5_dir = Path("data/top 5 dataset 30.8.25")
        if not top5_dir.exists():
            print("❌ Top 5 dataset directory not found!")
            return False
        
        # Group videos by word
        word_videos = {
            'doctor': [],
            'glasses': [],
            'help': [],
            'phone': [],
            'pillow': []
        }
        
        print("📊 Scanning available videos...")
        for video_file in top5_dir.glob("*.webm"):
            filename = video_file.name.lower()
            for word in word_videos.keys():
                if filename.startswith(word):
                    word_videos[word].append(video_file)
                    break
        
        print("📊 Available videos by class:")
        total_available = 0
        for word, videos in word_videos.items():
            print(f"   {word}: {len(videos)} videos")
            total_available += len(videos)
        
        print(f"   Total available: {total_available} videos")
        
        # Create massive validation directory
        massive_val_dir = Path("data/massive_validation_processed")
        massive_val_dir.mkdir(exist_ok=True)
        
        # Process videos for each class
        massive_manifest = []
        label_map = {'doctor': 0, 'glasses': 1, 'help': 2, 'phone': 3, 'pillow': 4}
        
        total_processed = 0
        total_failed = 0
        
        for word, videos in word_videos.items():
            print(f"\n🔄 Processing {word} videos...")
            
            # Take up to max_per_class videos
            selected_videos = videos[:max_per_class]
            processed_count = 0
            failed_count = 0
            
            for i, video_path in enumerate(tqdm(selected_videos, desc=f"Processing {word}")):
                # Preprocess video
                video_tensor = self.preprocess_video(video_path)
                
                if video_tensor is not None:
                    # Save processed tensor
                    output_filename = f"{word}_massive_{i:03d}_processed.pt"
                    output_path = massive_val_dir / output_filename
                    torch.save(video_tensor, output_path)
                    
                    # Add to manifest
                    massive_manifest.append({
                        'video_path': str(output_path),
                        'original_path': str(video_path),
                        'word': word,
                        'label': label_map[word],
                        'split': 'massive_val',
                        'speaker': f"massive_speaker_{i}",
                        'filename': video_path.name,
                        'processed_filename': output_filename,
                        'extraction_rate': 1.0
                    })
                    processed_count += 1
                    total_processed += 1
                else:
                    failed_count += 1
                    total_failed += 1
            
            print(f"   ✅ {word}: {processed_count} processed, {failed_count} failed")
        
        # Save massive validation manifest
        massive_df = pd.DataFrame(massive_manifest)
        massive_manifest_path = "data/massive_validation_manifest.csv"
        massive_df.to_csv(massive_manifest_path, index=False)
        
        print(f"\n🎉 Massive Validation Set Created!")
        print(f"   Total videos processed: {total_processed}")
        print(f"   Total videos failed: {total_failed}")
        print(f"   Success rate: {100*total_processed/(total_processed+total_failed):.1f}%")
        print(f"   📁 Manifest: {massive_manifest_path}")
        
        # Print class distribution
        print(f"\n📊 Final class distribution:")
        class_counts = massive_df['word'].value_counts()
        for word, count in class_counts.items():
            print(f"   {word}: {count} videos")
        
        return True

class R2Plus1DDataset(Dataset):
    """Dataset for R(2+1)D model with 16-frame clips"""
    
    def __init__(self, manifest_path, target_frames=16):
        self.manifest = pd.read_csv(manifest_path)
        self.target_frames = target_frames
        
        # Verify all files exist
        valid_rows = []
        for idx, row in self.manifest.iterrows():
            if Path(row['video_path']).exists():
                valid_rows.append(row)
        
        self.manifest = pd.DataFrame(valid_rows).reset_index(drop=True)
        
        print(f"📊 R(2+1)D Dataset loaded: {len(self.manifest)} videos")
        class_counts = self.manifest['word'].value_counts()
        print(f"   Class distribution: {dict(class_counts)}")
        
        # Kinetics-400 normalization stats
        self.normalize = transforms.Normalize(
            mean=[0.43216, 0.394666, 0.37645],
            std=[0.22803, 0.22145, 0.216989]
        )
    
    def __len__(self):
        return len(self.manifest)
    
    def __getitem__(self, idx):
        row = self.manifest.iloc[idx]
        
        # Load preprocessed tensor
        video_tensor = torch.load(row['video_path'])  # (1, T, H, W)
        
        # Ensure correct shape
        if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
            video_tensor = video_tensor.squeeze(0)  # (T, H, W)
        elif video_tensor.dim() == 3:
            pass  # Already (T, H, W)
        else:
            raise ValueError(f"Unexpected tensor shape: {video_tensor.shape}")
        
        # Sample exactly 16 frames
        T, H, W = video_tensor.shape
        if T >= self.target_frames:
            # Sample evenly
            indices = torch.linspace(0, T - 1, self.target_frames).long()
            video_tensor = video_tensor[indices]
        else:
            # Repeat frames to reach target
            repeat_factor = (self.target_frames + T - 1) // T
            video_tensor = video_tensor.repeat(repeat_factor, 1, 1)[:self.target_frames]
        
        # Convert grayscale to RGB: (T, H, W) -> (T, 3, H, W)
        video_tensor = video_tensor.unsqueeze(1).repeat(1, 3, 1, 1)
        
        # Normalize from [-1, 1] to [0, 1] first
        video_tensor = (video_tensor + 1.0) / 2.0
        
        # Apply Kinetics-400 normalization
        for t in range(self.target_frames):
            video_tensor[t] = self.normalize(video_tensor[t])
        
        # Rearrange to (3, T, H, W) for R(2+1)D
        video_tensor = video_tensor.permute(1, 0, 2, 3)
        
        label = torch.tensor(row['label'], dtype=torch.long)
        
        return video_tensor, label

def main():
    """Main function for massive validation expansion"""
    print("🎯 Massive Validation Set Expansion for >80% Accuracy")
    print("=" * 70)
    
    # Step 1: Create massive validation set
    expander = MassiveValidationExpander()
    success = expander.create_massive_validation_set(max_per_class=100)
    
    if not success:
        print("❌ Failed to create massive validation set")
        return False
    
    # Step 2: Load and verify the massive validation set
    massive_manifest_path = "data/massive_validation_manifest.csv"
    if Path(massive_manifest_path).exists():
        massive_df = pd.read_csv(massive_manifest_path)
        print(f"\n✅ Massive validation set ready: {len(massive_df)} videos")
        
        # Show final statistics
        class_counts = massive_df['word'].value_counts()
        print(f"📊 Final validation set distribution:")
        for word, count in class_counts.items():
            print(f"   {word}: {count} videos")
        
        print(f"\n🎯 Ready for R(2+1)D training with massive validation set!")
        print(f"   Original validation: 15 videos")
        print(f"   Massive validation: {len(massive_df)} videos")
        print(f"   Expansion factor: {len(massive_df)/15:.1f}x")
        
        return True
    else:
        print("❌ Massive validation manifest not found")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
