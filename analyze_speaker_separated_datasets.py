#!/usr/bin/env python3
"""
Analyze the new speaker-separated datasets to verify proper separation and balance.
"""

import os
import cv2
import pandas as pd
import numpy as np
from pathlib import Path
import json
from collections import defaultdict, Counter
import re

def extract_speaker_id_from_filename(filename):
    """
    Extract speaker ID from filename.
    Assumes format like 'word X.mp4' where X is the video number.
    We'll use the video number as a proxy for speaker ID.
    """
    # Extract number from filename like "doctor 1.mp4" -> "1"
    match = re.search(r'(\w+)\s+(\d+)\.mp4', filename)
    if match:
        word = match.group(1)
        video_num = match.group(2)
        return f"speaker_{video_num}"
    return "unknown"

def analyze_video_properties(video_path):
    """Analyze basic video properties"""
    try:
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            return None
            
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps if fps > 0 else 0
        
        cap.release()
        
        return {
            'frame_count': frame_count,
            'fps': fps,
            'width': width,
            'height': height,
            'duration': duration
        }
    except Exception as e:
        print(f"Error analyzing {video_path}: {e}")
        return None

def analyze_dataset_folder(folder_path, dataset_name):
    """Analyze a single dataset folder"""
    folder_path = Path(folder_path)
    
    if not folder_path.exists():
        print(f"❌ Dataset folder not found: {folder_path}")
        return None
    
    print(f"\n📁 Analyzing {dataset_name}: {folder_path}")
    print("-" * 50)
    
    # Collect all videos
    video_files = list(folder_path.glob("*.mp4"))
    print(f"   Total videos: {len(video_files)}")
    
    if not video_files:
        print("   ⚠️  No MP4 files found!")
        return None
    
    # Analyze by word and speaker
    word_data = defaultdict(list)
    speaker_data = defaultdict(list)
    all_speakers = set()
    
    for video_file in video_files:
        # Extract word and speaker from filename
        filename = video_file.name
        speaker_id = extract_speaker_id_from_filename(filename)
        
        # Extract word (first part of filename)
        word = filename.split()[0].lower()
        
        # Analyze video properties
        props = analyze_video_properties(video_file)
        
        video_info = {
            'filename': filename,
            'word': word,
            'speaker_id': speaker_id,
            'path': str(video_file),
            'properties': props
        }
        
        word_data[word].append(video_info)
        speaker_data[speaker_id].append(video_info)
        all_speakers.add(speaker_id)
    
    # Print word distribution
    print(f"\n   📊 Word Distribution:")
    for word, videos in sorted(word_data.items()):
        print(f"      {word}: {len(videos)} videos")
    
    # Print speaker distribution
    print(f"\n   👥 Speaker Distribution:")
    for speaker_id, videos in sorted(speaker_data.items()):
        words_by_speaker = Counter(v['word'] for v in videos)
        print(f"      {speaker_id}: {len(videos)} videos - {dict(words_by_speaker)}")
    
    # Check balance
    word_counts = {word: len(videos) for word, videos in word_data.items()}
    is_balanced = len(set(word_counts.values())) <= 1  # All counts are the same
    
    print(f"\n   ⚖️  Dataset Balance:")
    print(f"      Balanced: {'✅' if is_balanced else '❌'}")
    if not is_balanced:
        min_count = min(word_counts.values())
        max_count = max(word_counts.values())
        print(f"      Range: {min_count} - {max_count} videos per word")
    
    # Video quality analysis
    if any(v['properties'] for videos in word_data.values() for v in videos if v['properties']):
        all_props = [v['properties'] for videos in word_data.values() for v in videos if v['properties']]
        
        avg_duration = np.mean([p['duration'] for p in all_props])
        avg_fps = np.mean([p['fps'] for p in all_props])
        avg_width = np.mean([p['width'] for p in all_props])
        avg_height = np.mean([p['height'] for p in all_props])
        
        print(f"\n   🎥 Video Quality:")
        print(f"      Average duration: {avg_duration:.2f}s")
        print(f"      Average FPS: {avg_fps:.1f}")
        print(f"      Average resolution: {avg_width:.0f}x{avg_height:.0f}")
    
    return {
        'dataset_name': dataset_name,
        'total_videos': len(video_files),
        'word_data': dict(word_data),
        'speaker_data': dict(speaker_data),
        'all_speakers': list(all_speakers),
        'word_counts': word_counts,
        'is_balanced': is_balanced
    }

def check_speaker_separation(train_data, val_data, test_data):
    """Check if speakers are properly separated across datasets"""
    print(f"\n🔍 Checking Speaker Separation")
    print("=" * 50)
    
    if not all([train_data, val_data, test_data]):
        print("❌ Missing dataset information")
        return False
    
    train_speakers = set(train_data['all_speakers'])
    val_speakers = set(val_data['all_speakers'])
    test_speakers = set(test_data['all_speakers'])
    
    print(f"   Training speakers: {len(train_speakers)}")
    print(f"   Validation speakers: {len(val_speakers)}")
    print(f"   Test speakers: {len(test_speakers)}")
    
    # Check for overlaps
    train_val_overlap = train_speakers & val_speakers
    train_test_overlap = train_speakers & test_speakers
    val_test_overlap = val_speakers & test_speakers
    
    print(f"\n   🔄 Speaker Overlaps:")
    print(f"      Train-Val overlap: {len(train_val_overlap)} speakers")
    if train_val_overlap:
        print(f"         {sorted(list(train_val_overlap))}")
    
    print(f"      Train-Test overlap: {len(train_test_overlap)} speakers")
    if train_test_overlap:
        print(f"         {sorted(list(train_test_overlap))}")
    
    print(f"      Val-Test overlap: {len(val_test_overlap)} speakers")
    if val_test_overlap:
        print(f"         {sorted(list(val_test_overlap))}")
    
    # Perfect separation means no overlaps
    perfect_separation = not (train_val_overlap or train_test_overlap or val_test_overlap)
    
    print(f"\n   ✅ Perfect Speaker Separation: {'Yes' if perfect_separation else 'No'}")
    
    return perfect_separation

def generate_manifests(train_data, val_data, test_data):
    """Generate manifest files for the speaker-separated datasets"""
    print(f"\n📝 Generating Manifest Files")
    print("=" * 30)
    
    output_dir = Path("data/speaker_separated_manifests")
    output_dir.mkdir(exist_ok=True)
    
    words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
    
    # Generate training manifest
    if train_data:
        train_manifest = []
        for word in words:
            if word in train_data['word_data']:
                for video_info in train_data['word_data'][word]:
                    train_manifest.append({
                        'video_path': video_info['path'],
                        'word': word,
                        'label': words.index(word),
                        'split': 'train',
                        'speaker': video_info['speaker_id'],
                        'filename': video_info['filename']
                    })
        
        train_df = pd.DataFrame(train_manifest)
        train_path = output_dir / "train_manifest.csv"
        train_df.to_csv(train_path, index=False)
        print(f"   ✅ Training manifest: {train_path} ({len(train_df)} videos)")
    
    # Generate validation manifest
    if val_data:
        val_manifest = []
        for word in words:
            if word in val_data['word_data']:
                for video_info in val_data['word_data'][word]:
                    val_manifest.append({
                        'video_path': video_info['path'],
                        'word': word,
                        'label': words.index(word),
                        'split': 'val',
                        'speaker': video_info['speaker_id'],
                        'filename': video_info['filename']
                    })
        
        val_df = pd.DataFrame(val_manifest)
        val_path = output_dir / "val_manifest.csv"
        val_df.to_csv(val_path, index=False)
        print(f"   ✅ Validation manifest: {val_path} ({len(val_df)} videos)")
    
    # Generate test manifest
    if test_data:
        test_manifest = []
        for word in words:
            if word in test_data['word_data']:
                for video_info in test_data['word_data'][word]:
                    test_manifest.append({
                        'video_path': video_info['path'],
                        'word': word,
                        'label': words.index(word),
                        'split': 'test',
                        'speaker': video_info['speaker_id'],
                        'filename': video_info['filename']
                    })
        
        test_df = pd.DataFrame(test_manifest)
        test_path = output_dir / "test_manifest.csv"
        test_df.to_csv(test_path, index=False)
        print(f"   ✅ Test manifest: {test_path} ({len(test_df)} videos)")
    
    # Combined manifest
    all_manifests = []
    if train_data:
        all_manifests.extend(train_manifest)
    if val_data:
        all_manifests.extend(val_manifest)
    if test_data:
        all_manifests.extend(test_manifest)
    
    if all_manifests:
        combined_df = pd.DataFrame(all_manifests)
        combined_path = output_dir / "combined_manifest.csv"
        combined_df.to_csv(combined_path, index=False)
        print(f"   ✅ Combined manifest: {combined_path} ({len(combined_df)} videos)")
    
    return output_dir

def main():
    """Main analysis function"""
    print("🔍 Speaker-Separated Dataset Analysis")
    print("=" * 60)
    
    # Analyze each dataset
    train_data = analyze_dataset_folder("data/TRAINING SET", "Training Set")
    val_data = analyze_dataset_folder("data/VAL SET", "Validation Set")
    test_data = analyze_dataset_folder("data/TEST SET", "Test Set")
    
    # Check speaker separation
    speaker_separation_ok = check_speaker_separation(train_data, val_data, test_data)
    
    # Generate manifests
    manifest_dir = generate_manifests(train_data, val_data, test_data)
    
    # Overall summary
    print(f"\n📊 Overall Dataset Summary")
    print("=" * 40)
    
    total_videos = 0
    total_speakers = set()
    
    if train_data:
        total_videos += train_data['total_videos']
        total_speakers.update(train_data['all_speakers'])
        print(f"   Training: {train_data['total_videos']} videos, {len(train_data['all_speakers'])} speakers")
    
    if val_data:
        total_videos += val_data['total_videos']
        total_speakers.update(val_data['all_speakers'])
        print(f"   Validation: {val_data['total_videos']} videos, {len(val_data['all_speakers'])} speakers")
    
    if test_data:
        total_videos += test_data['total_videos']
        total_speakers.update(test_data['all_speakers'])
        print(f"   Test: {test_data['total_videos']} videos, {len(test_data['all_speakers'])} speakers")
    
    print(f"\n   📈 Totals:")
    print(f"      Total videos: {total_videos}")
    print(f"      Unique speakers: {len(total_speakers)}")
    print(f"      Speaker separation: {'✅ Perfect' if speaker_separation_ok else '❌ Issues found'}")
    
    # Save analysis results
    analysis_results = {
        'train_data': train_data,
        'val_data': val_data,
        'test_data': test_data,
        'speaker_separation_perfect': speaker_separation_ok,
        'total_videos': total_videos,
        'total_speakers': len(total_speakers),
        'manifest_directory': str(manifest_dir)
    }
    
    results_path = Path("speaker_separated_analysis_results.json")
    with open(results_path, 'w') as f:
        json.dump(analysis_results, f, indent=2, default=str)
    
    print(f"\n💾 Analysis results saved: {results_path}")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    if speaker_separation_ok:
        print(f"   ✅ Speaker separation is perfect - ready for training!")
        print(f"   📋 Use manifests in: {manifest_dir}")
        print(f"   🚀 Proceed with preprocessing and training")
    else:
        print(f"   ⚠️  Speaker separation issues detected")
        print(f"   🔧 Review dataset splits before training")
        print(f"   📊 Consider manual verification of speaker assignments")
    
    if total_videos < 50:
        print(f"   ⚠️  Small dataset size ({total_videos} videos)")
        print(f"   💡 Consider aggressive data augmentation")
        print(f"   🎯 Lower initial accuracy expectations")
    
    return analysis_results

if __name__ == "__main__":
    results = main()
