#!/usr/bin/env python3
"""
Phase 2: Enhanced training with all battle-tested recommendations.
Key improvements:
1. Balanced batches with WeightedRandomSampler
2. Focal loss with class weights  
3. OneCycleLR scheduler
4. TTA for validation
5. Expanded validation set using available data
6. ArcFace head (fixed implementation)
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset, WeightedRandomSampler
import torchvision.models.video as video_models
import torchvision.transforms as transforms
import pandas as pd
import numpy as np
from pathlib import Path
import json
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, f1_score
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

class FocalLoss(nn.Module):
    """Focal Loss with class weights"""
    
    def __init__(self, alpha, gamma=2.0, reduction='mean'):
        super().__init__()
        self.alpha = torch.tensor(alpha, dtype=torch.float)
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, logits, targets):
        ce = F.cross_entropy(logits, targets, weight=self.alpha.to(logits.device), reduction='none')
        pt = torch.exp(-ce)
        loss = ((1-pt) ** self.gamma) * ce
        return loss.mean() if self.reduction == 'mean' else loss.sum()

class ArcMarginProduct(nn.Module):
    """Fixed ArcFace head with proper inference path"""
    
    def __init__(self, in_features, out_features, s=30.0, m=0.3):
        super().__init__()
        self.W = nn.Parameter(torch.randn(out_features, in_features))
        nn.init.xavier_uniform_(self.W)
        self.s, self.m = s, m
    
    def forward(self, x, labels=None):
        x = F.normalize(x)
        W = F.normalize(self.W)
        cos = (x @ W.t()).clamp(-1, 1)
        
        if labels is None or not self.training:
            # Inference path - no margin
            return self.s * cos
        
        # Training path - add margin
        theta = torch.acos(cos)
        target = torch.cos(theta + self.m)
        onehot = F.one_hot(labels, num_classes=cos.size(1)).float()
        logits = self.s * (onehot * target + (1 - onehot) * cos)
        return logits

class EnhancedDataset(Dataset):
    """Enhanced dataset with proper augmentation"""
    
    def __init__(self, manifest_path, target_frames=16, augment=True, train_stats=None):
        self.manifest = pd.read_csv(manifest_path)
        self.target_frames = target_frames
        self.augment = augment
        
        # Verify all files exist
        valid_rows = []
        for idx, row in self.manifest.iterrows():
            if Path(row['video_path']).exists():
                valid_rows.append(row)
        
        self.manifest = pd.DataFrame(valid_rows).reset_index(drop=True)
        
        print(f"📊 Dataset loaded: {len(self.manifest)} videos")
        class_counts = self.manifest['word'].value_counts()
        print(f"   Class distribution: {dict(class_counts)}")
        
        # Compute dataset statistics
        if train_stats is None:
            self.compute_dataset_stats()
        else:
            self.mean, self.std = train_stats
        
        print(f"   Using normalization - Mean: {self.mean:.4f}, Std: {self.std:.4f}")
        
        # Enhanced augmentation
        if augment:
            self.spatial_transforms = transforms.Compose([
                transforms.RandomResizedCrop(size=(96, 96), scale=(0.88, 1.0)),
                transforms.RandomRotation(degrees=4),
                transforms.ColorJitter(brightness=0.1, contrast=0.1),
            ])
        else:
            self.spatial_transforms = transforms.Resize((96, 96))
    
    def compute_dataset_stats(self):
        """Compute mean and std from the dataset"""
        print("🔄 Computing dataset statistics...")
        all_pixels = []
        
        sample_size = min(30, len(self.manifest))
        for idx in tqdm(range(sample_size), desc="Computing stats"):
            row = self.manifest.iloc[idx]
            try:
                video_tensor = torch.load(row['video_path'])
                if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
                    video_tensor = video_tensor.squeeze(0)
                all_pixels.append(video_tensor.flatten())
            except Exception as e:
                continue
        
        if all_pixels:
            all_pixels = torch.cat(all_pixels)
            self.mean = all_pixels.mean().item()
            self.std = all_pixels.std().item()
        else:
            self.mean, self.std = 0.0, 1.0
    
    def __len__(self):
        return len(self.manifest)
    
    def __getitem__(self, idx):
        row = self.manifest.iloc[idx]
        
        try:
            video_tensor = torch.load(row['video_path'])
        except Exception as e:
            video_tensor = torch.randn(1, 64, 112, 112)
        
        # Ensure correct shape
        if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
            video_tensor = video_tensor.squeeze(0)
        elif video_tensor.dim() == 3:
            pass
        else:
            video_tensor = torch.randn(64, 112, 112)
        
        # Temporal sampling with augmentation
        T, H, W = video_tensor.shape
        if T >= self.target_frames:
            if self.augment and np.random.random() > 0.5:
                # Random temporal crop (±20%)
                start_range = int(T * 0.2)
                start_idx = np.random.randint(0, max(1, T - self.target_frames - start_range))
                indices = torch.arange(start_idx, start_idx + self.target_frames)
            else:
                # Uniform sampling
                indices = torch.linspace(0, T - 1, self.target_frames).long()
            video_tensor = video_tensor[indices]
        else:
            # Repeat frames
            repeat_factor = (self.target_frames + T - 1) // T
            video_tensor = video_tensor.repeat(repeat_factor, 1, 1)[:self.target_frames]
        
        # Convert to 3-channel
        video_tensor = video_tensor.unsqueeze(1).repeat(1, 3, 1, 1)
        
        # Normalize from [-1, 1] to [0, 1]
        video_tensor = (video_tensor + 1.0) / 2.0
        
        # Apply spatial augmentation
        if self.augment and np.random.random() > 0.5:
            augmented_frames = []
            for t in range(self.target_frames):
                frame = video_tensor[t]
                frame = self.spatial_transforms(frame)
                augmented_frames.append(frame)
            video_tensor = torch.stack(augmented_frames)
        else:
            video_tensor = torch.stack([transforms.Resize((96, 96))(video_tensor[t]) for t in range(self.target_frames)])
        
        # Normalize with dataset statistics
        video_tensor = (video_tensor - self.mean) / self.std
        
        # Rearrange to (3, T, H, W)
        video_tensor = video_tensor.permute(1, 0, 2, 3)
        
        label = torch.tensor(row['label'], dtype=torch.long)
        
        return video_tensor, label

class EnhancedR2Plus1DModel(nn.Module):
    """Enhanced R(2+1)D model with ArcFace head"""
    
    def __init__(self, num_classes=5, dropout=0.4, use_arcface=True):
        super().__init__()
        
        # Load pretrained R(2+1)D-18
        try:
            self.backbone = video_models.r2plus1d_18(weights='KINETICS400_V1')
        except TypeError:
            self.backbone = video_models.r2plus1d_18(pretrained=True)
        
        # Remove final classifier
        self.backbone.fc = nn.Identity()
        
        # Dropout after backbone
        self.dropout = nn.Dropout(dropout)
        
        # Classifier head
        self.use_arcface = use_arcface
        if use_arcface:
            self.classifier = ArcMarginProduct(512, num_classes, s=30.0, m=0.3)
        else:
            self.classifier = nn.Linear(512, num_classes)
        
        print(f"🤖 EnhancedR2Plus1DModel created")
        print(f"   Backbone: R(2+1)D-18")
        print(f"   ArcFace head: {use_arcface}")
        print(f"   Dropout: {dropout}")
    
    def forward(self, x, labels=None):
        # x: (batch, 3, T, H, W)
        features = self.backbone(x)  # (batch, 512)
        features = self.dropout(features)
        
        if self.use_arcface:
            logits = self.classifier(features, labels)
        else:
            logits = self.classifier(features)
        
        return logits

def create_balanced_sampler(dataset):
    """Create weighted sampler for balanced batches"""
    labels = [dataset.manifest.iloc[i]['label'] for i in range(len(dataset))]
    class_counts = np.bincount(labels)
    
    # Compute class weights (inverse frequency)
    class_weights = 1.0 / class_counts
    
    # Oversample help and glasses (classes 2 and 1)
    class_weights[1] *= 2.0  # glasses
    class_weights[2] *= 2.0  # help
    
    # Normalize
    class_weights = class_weights / class_weights.sum() * len(class_weights)
    
    sample_weights = [class_weights[label] for label in labels]
    
    print(f"📊 Class weights: {class_weights}")
    
    return WeightedRandomSampler(
        sample_weights, 
        num_samples=len(sample_weights), 
        replacement=True
    )

def predict_with_tta(model, clip, device):
    """Test-Time Augmentation with temporal crops"""
    model.eval()
    T = clip.shape[2]  # (3, T, H, W)
    
    # Three temporal crops: start, middle, end
    crops = []
    thirds = [0, max(0, T//2-8), max(0, T-16)]
    
    for start in thirds:
        end = min(T, start + 16)
        seg = clip[:, :, start:end]  # (3, 16, H, W)
        
        if seg.shape[2] < 16:  # Pad if needed
            pad_frames = 16 - seg.shape[2]
            pad = seg[:, :, -1:].repeat(1, 1, pad_frames, 1, 1)
            seg = torch.cat([seg, pad], dim=2)
        
        crops.append(seg)
    
    # Predict on all crops
    with torch.no_grad():
        logits = []
        for crop in crops:
            crop = crop.unsqueeze(0).to(device)  # Add batch dim
            output = model(crop, labels=None)  # No labels for inference
            logits.append(F.softmax(output, dim=-1))
    
    # Average predictions
    return torch.stack(logits).mean(0)  # (1, num_classes)

def expand_validation_set():
    """Create larger validation set using available data"""
    print("🔄 Expanding validation set...")
    
    # Load existing data
    train_manifest = "data/5word_processed/train_processed_manifest.csv"
    val_manifest = "data/5word_processed/val_processed_manifest.csv"
    
    if Path(train_manifest).exists() and Path(val_manifest).exists():
        train_df = pd.read_csv(train_manifest)
        val_df = pd.read_csv(val_manifest)
        all_df = pd.concat([train_df, val_df], ignore_index=True)
    else:
        print("❌ Processed manifests not found")
        return None, None
    
    print(f"📊 Total available data: {len(all_df)} videos")
    
    # Filter out male 18-39 if demographic info available
    if 'filename' in all_df.columns:
        filtered_df = all_df[~all_df['filename'].str.contains('18to39.*male', case=False, na=False)]
        print(f"📊 After filtering male 18-39: {len(filtered_df)}")
    else:
        filtered_df = all_df
    
    # Create 60/40 split for larger validation set
    X = filtered_df.drop(['label'], axis=1)
    y = filtered_df['label']
    
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, 
        test_size=0.4,  # 40% for validation (larger than before)
        stratify=y,
        random_state=42
    )
    
    # Recreate dataframes
    train_df = pd.concat([X_train, y_train], axis=1)
    val_df = pd.concat([X_val, y_val], axis=1)
    
    # Save new splits
    train_df.to_csv("data/enhanced_train_manifest.csv", index=False)
    val_df.to_csv("data/enhanced_val_manifest.csv", index=False)
    
    print(f"✅ Enhanced split created:")
    print(f"   Training: {len(train_df)} videos")
    print(f"   Validation: {len(val_df)} videos")
    
    # Show class distribution
    val_class_counts = val_df['word'].value_counts()
    print(f"   Validation class distribution: {dict(val_class_counts)}")
    
    return "data/enhanced_train_manifest.csv", "data/enhanced_val_manifest.csv"

class EnhancedTrainer:
    """Enhanced trainer with all battle-tested features"""
    
    def __init__(self, use_arcface=True):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  Using device: {self.device}")
        
        self.batch_size = 16  # Effective batch size with grad accumulation
        self.use_arcface = use_arcface
        
        # Create output directory
        self.output_dir = Path("artifacts/enhanced_v1")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🎯 Enhanced Training Pipeline:")
        print(f"   Batch size: {self.batch_size}")
        print(f"   ArcFace head: {use_arcface}")
        print(f"   Balanced sampling: Enabled")
        print(f"   TTA validation: Enabled")
    
    def create_data_loaders(self, train_manifest, val_manifest):
        """Create enhanced data loaders"""
        
        # Training dataset with augmentation
        train_dataset = EnhancedDataset(
            train_manifest,
            target_frames=16,
            augment=True
        )
        
        # Get training stats
        train_stats = (train_dataset.mean, train_dataset.std)
        
        # Validation dataset without augmentation
        val_dataset = EnhancedDataset(
            val_manifest,
            target_frames=16,
            augment=False,
            train_stats=train_stats
        )
        
        # Create balanced sampler for training
        balanced_sampler = create_balanced_sampler(train_dataset)
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            sampler=balanced_sampler,
            num_workers=2,
            pin_memory=True,
            drop_last=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=2,
            pin_memory=True
        )
        
        return train_loader, val_loader, train_dataset

    def train_model(self, model, train_loader, val_loader, train_dataset):
        """Enhanced training with all battle-tested techniques"""
        print("\n🚀 Enhanced Training with Battle-Tested Improvements")
        print("=" * 70)

        # Get class counts for focal loss
        labels = [train_dataset.manifest.iloc[i]['label'] for i in range(len(train_dataset))]
        class_counts = np.bincount(labels)

        # Compute alpha for focal loss (inverse frequency normalized)
        alpha = 1.0 / class_counts
        alpha = alpha / alpha.sum() * len(class_counts)

        print(f"📊 Class counts: {class_counts}")
        print(f"📊 Focal loss alpha: {alpha}")

        # Optimizer with proper weight decay
        optimizer = optim.AdamW(
            model.parameters(),
            lr=1e-3,
            weight_decay=1e-4,
            betas=(0.9, 0.999)
        )

        # OneCycleLR scheduler
        scheduler = optim.lr_scheduler.OneCycleLR(
            optimizer,
            max_lr=1e-3,
            steps_per_epoch=len(train_loader),
            epochs=50,
            pct_start=0.3,
            div_factor=10
        )

        # Focal loss with class weights
        criterion = FocalLoss(alpha=alpha, gamma=2.0)

        best_val_acc = 0.0
        best_macro_f1 = 0.0
        epochs = 50
        patience = 20
        epochs_without_improvement = 0
        min_epochs_before_stopping = 20

        words = ['doctor', 'glasses', 'help', 'phone', 'pillow']

        for epoch in range(epochs):
            print(f"\n📅 Epoch {epoch+1}/{epochs}")

            # Training phase
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0

            for batch_idx, (videos, labels) in enumerate(tqdm(train_loader, desc="Training")):
                videos, labels = videos.to(self.device), labels.to(self.device)

                optimizer.zero_grad()

                if self.use_arcface:
                    outputs = model(videos, labels)
                else:
                    outputs = model(videos)

                loss = criterion(outputs, labels)
                loss.backward()

                # Check gradient norms
                total_norm = torch.sqrt(sum((p.grad.detach()**2).sum() for p in model.parameters() if p.grad is not None))

                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)

                optimizer.step()
                scheduler.step()

                train_loss += loss.item()
                _, predicted = outputs.max(1)
                train_total += labels.size(0)
                train_correct += predicted.eq(labels).sum().item()

                # Print first batch details
                if batch_idx == 0:
                    print(f"   Batch 0: loss={loss.item():.3f}, grad_norm={total_norm.item():.3f}")
                    print(f"   Labels: {labels.cpu().numpy()}")
                    print(f"   Preds:  {predicted.cpu().numpy()}")

            # Validation phase with TTA
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            all_predictions = []
            all_labels = []

            with torch.no_grad():
                for videos, labels in tqdm(val_loader, desc="Validation"):
                    labels = labels.to(self.device)

                    # Use TTA for validation
                    batch_predictions = []
                    for i in range(videos.size(0)):
                        tta_pred = predict_with_tta(model, videos[i], self.device)
                        batch_predictions.append(tta_pred)

                    outputs = torch.cat(batch_predictions, dim=0)
                    loss = criterion(outputs, labels)

                    val_loss += loss.item()
                    _, predicted = outputs.max(1)
                    val_total += labels.size(0)
                    val_correct += predicted.eq(labels).sum().item()

                    all_predictions.extend(predicted.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())

            # Calculate metrics
            train_acc = 100. * train_correct / train_total
            val_acc = 100. * val_correct / val_total
            macro_f1 = f1_score(all_labels, all_predictions, average='macro') * 100
            current_lr = scheduler.get_last_lr()[0]

            print(f"📊 Results:")
            print(f"   Train Loss: {train_loss/len(train_loader):.4f} | Train Acc: {train_acc:.2f}%")
            print(f"   Val Loss: {val_loss/len(val_loader):.4f} | Val Acc: {val_acc:.2f}%")
            print(f"   Macro F1: {macro_f1:.2f}% | LR: {current_lr:.6f}")

            # Confusion matrix and per-class accuracy
            cm = confusion_matrix(all_labels, all_predictions, labels=list(range(5)))
            print(f"\n🔍 Confusion Matrix (Epoch {epoch+1}):")
            print("     ", " ".join([f"{word:>8}" for word in words]))
            for i, word in enumerate(words):
                print(f"{word:>8}: {' '.join([f'{cm[i,j]:>8}' for j in range(len(words))])}")

            per_class_acc = cm.diagonal() / cm.sum(axis=1) * 100
            print(f"\n📊 Per-class Accuracy:")
            for i, word in enumerate(words):
                print(f"   {word}: {per_class_acc[i]:.1f}%")

            # Check for improvement (prioritize macro F1)
            improved = macro_f1 > best_macro_f1 or (macro_f1 == best_macro_f1 and val_acc > best_val_acc)

            if improved:
                best_val_acc = max(best_val_acc, val_acc)
                best_macro_f1 = max(best_macro_f1, macro_f1)
                epochs_without_improvement = 0

                # Save best model
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'val_accuracy': val_acc,
                    'macro_f1': macro_f1,
                    'training_config': {
                        'batch_size': self.batch_size,
                        'learning_rate': 1e-3,
                        'architecture': 'Enhanced_R2Plus1D_ArcFace',
                        'focal_loss': True,
                        'tta': True,
                        'balanced_sampling': True
                    }
                }, self.output_dir / 'enhanced_best.pth')

                print(f"🎉 NEW BEST: Val Acc {val_acc:.2f}%, Macro F1 {macro_f1:.2f}%!")

                # Detailed classification report
                report = classification_report(all_labels, all_predictions, target_names=words, digits=3)
                print(f"\n📋 Classification Report:")
                print(report)

                # Check if target achieved
                if val_acc >= 80.0 and macro_f1 >= 75.0:
                    print(f"\n🎯 TARGET ACHIEVED: 80% val acc + 75% macro F1!")
                    return True, best_val_acc, best_macro_f1

            else:
                epochs_without_improvement += 1

            # Early stopping with minimum epochs
            if epochs_without_improvement >= patience and epoch >= min_epochs_before_stopping:
                print(f"\n⏹️  Early stopping: No improvement for {patience} epochs (after {epoch+1} epochs)")
                break
            elif epoch < min_epochs_before_stopping:
                print(f"   Continuing training (minimum {min_epochs_before_stopping} epochs required)")

        print(f"\n✅ Enhanced training completed!")
        print(f"   Best validation accuracy: {best_val_acc:.2f}%")
        print(f"   Best macro F1: {best_macro_f1:.2f}%")

        return best_val_acc >= 80.0, best_val_acc, best_macro_f1

def main():
    """Main function implementing Phase 2 enhanced training"""
    print("🎯 Phase 2: Enhanced Training with Battle-Tested Improvements")
    print("=" * 80)

    # Step 1: Expand validation set
    train_manifest, val_manifest = expand_validation_set()

    if train_manifest is None:
        print("❌ Failed to create enhanced train/val split")
        return False

    # Step 2: Create enhanced trainer
    trainer = EnhancedTrainer(use_arcface=True)

    # Step 3: Create data loaders with balanced sampling
    train_loader, val_loader, train_dataset = trainer.create_data_loaders(train_manifest, val_manifest)

    # Step 4: Create enhanced model
    model = EnhancedR2Plus1DModel(
        num_classes=5,
        dropout=0.4,
        use_arcface=True
    )
    model.to(trainer.device)

    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"📊 Model Statistics:")
    print(f"   Total parameters: {total_params:,}")
    print(f"   Trainable parameters: {trainable_params:,}")

    # Step 5: Train model with all improvements
    success, final_acc, final_f1 = trainer.train_model(model, train_loader, val_loader, train_dataset)

    if success:
        print(f"\n🎉 SUCCESS: Achieved {final_acc:.2f}% val acc + {final_f1:.2f}% macro F1!")
        print(f"📁 Model saved: {trainer.output_dir}/enhanced_best.pth")
        return True
    else:
        print(f"\n📊 Final results: {final_acc:.2f}% val acc, {final_f1:.2f}% macro F1")
        if final_acc >= 60.0:
            print("🔄 Good progress - consider ensemble training with multiple seeds")
        else:
            print("💡 May need larger dataset or different approach")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
