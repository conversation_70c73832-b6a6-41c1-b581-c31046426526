#!/usr/bin/env python3
"""
Fix speaker separation issues by reassigning videos to ensure no speaker overlap.
"""

import os
import shutil
import pandas as pd
import numpy as np
from pathlib import Path
import json
from collections import defaultdict, Counter
import re

def load_current_analysis():
    """Load the current dataset analysis"""
    analysis_path = Path("current_dataset_analysis.json")
    if not analysis_path.exists():
        print("❌ Current analysis not found. Run analyze_current_datasets.py first.")
        return None
    
    with open(analysis_path, 'r') as f:
        return json.load(f)

def create_speaker_separated_splits(analysis_data):
    """Create proper speaker-separated splits"""
    print("🔧 Creating proper speaker-separated splits...")
    
    # Collect all videos with speaker information
    all_videos = []
    
    for split_name in ['train_data', 'val_data', 'test_data']:
        if analysis_data[split_name]:
            for video in analysis_data[split_name]['videos_data']:
                video['original_split'] = split_name.replace('_data', '')
                all_videos.append(video)
    
    print(f"   Total videos to reassign: {len(all_videos)}")
    
    # Group videos by speaker
    speaker_videos = defaultdict(list)
    for video in all_videos:
        speaker_videos[video['speaker_id']].append(video)
    
    print(f"   Total speakers: {len(speaker_videos)}")
    
    # Check each speaker has all 5 words (or at least multiple words)
    complete_speakers = []
    partial_speakers = []
    
    for speaker_id, videos in speaker_videos.items():
        words = set(v['word'] for v in videos)
        if len(words) >= 4:  # Speakers with 4+ words
            complete_speakers.append((speaker_id, videos, words))
        else:
            partial_speakers.append((speaker_id, videos, words))
    
    print(f"   Complete speakers (4+ words): {len(complete_speakers)}")
    print(f"   Partial speakers (<4 words): {len(partial_speakers)}")
    
    # Sort speakers by number of videos (descending) for better distribution
    complete_speakers.sort(key=lambda x: len(x[1]), reverse=True)
    partial_speakers.sort(key=lambda x: len(x[1]), reverse=True)
    
    # Assign speakers to splits
    # Target: ~70% train, ~15% val, ~15% test
    total_speakers = len(complete_speakers) + len(partial_speakers)
    target_train = int(total_speakers * 0.7)
    target_val = int(total_speakers * 0.15)
    
    # Start with complete speakers for better balance
    train_speakers = []
    val_speakers = []
    test_speakers = []
    
    # Assign complete speakers first
    for i, (speaker_id, videos, words) in enumerate(complete_speakers):
        if len(train_speakers) < target_train:
            train_speakers.append((speaker_id, videos, words))
        elif len(val_speakers) < target_val:
            val_speakers.append((speaker_id, videos, words))
        else:
            test_speakers.append((speaker_id, videos, words))
    
    # Assign partial speakers
    for speaker_id, videos, words in partial_speakers:
        if len(train_speakers) < target_train:
            train_speakers.append((speaker_id, videos, words))
        elif len(val_speakers) < target_val:
            val_speakers.append((speaker_id, videos, words))
        else:
            test_speakers.append((speaker_id, videos, words))
    
    print(f"\n   📊 Speaker assignment:")
    print(f"      Training: {len(train_speakers)} speakers")
    print(f"      Validation: {len(val_speakers)} speakers")
    print(f"      Test: {len(test_speakers)} speakers")
    
    # Create video splits
    train_videos = []
    val_videos = []
    test_videos = []
    
    for speaker_id, videos, words in train_speakers:
        train_videos.extend(videos)
    
    for speaker_id, videos, words in val_speakers:
        val_videos.extend(videos)
    
    for speaker_id, videos, words in test_speakers:
        test_videos.extend(videos)
    
    # Check word distribution
    def check_word_distribution(videos, split_name):
        word_counts = Counter(v['word'] for v in videos)
        print(f"   {split_name}: {len(videos)} videos")
        for word in ['doctor', 'glasses', 'help', 'phone', 'pillow']:
            count = word_counts.get(word, 0)
            print(f"      {word}: {count}")
        return word_counts
    
    print(f"\n   📊 Word distribution after reassignment:")
    train_word_counts = check_word_distribution(train_videos, "Training")
    val_word_counts = check_word_distribution(val_videos, "Validation")
    test_word_counts = check_word_distribution(test_videos, "Test")
    
    # Check for missing words in any split
    all_words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
    missing_in_train = [w for w in all_words if train_word_counts.get(w, 0) == 0]
    missing_in_val = [w for w in all_words if val_word_counts.get(w, 0) == 0]
    missing_in_test = [w for w in all_words if test_word_counts.get(w, 0) == 0]
    
    if missing_in_train or missing_in_val or missing_in_test:
        print(f"\n   ⚠️  Missing words detected:")
        if missing_in_train:
            print(f"      Training missing: {missing_in_train}")
        if missing_in_val:
            print(f"      Validation missing: {missing_in_val}")
        if missing_in_test:
            print(f"      Test missing: {missing_in_test}")
        
        # Try to rebalance by moving some speakers
        print(f"   🔧 Attempting rebalancing...")
        
        # Simple rebalancing: move speakers between splits to ensure all words are present
        # This is a heuristic approach - more sophisticated balancing could be implemented
        
        for missing_word in missing_in_val:
            # Find a speaker in train or test that has this word
            for i, (speaker_id, videos, words) in enumerate(train_speakers + test_speakers):
                if missing_word in words:
                    # Move this speaker to validation
                    if i < len(train_speakers):
                        # Remove from train
                        moved_speaker = train_speakers.pop(i)
                        train_videos = [v for v in train_videos if v['speaker_id'] != speaker_id]
                    else:
                        # Remove from test
                        test_idx = i - len(train_speakers)
                        moved_speaker = test_speakers.pop(test_idx)
                        test_videos = [v for v in test_videos if v['speaker_id'] != speaker_id]
                    
                    # Add to validation
                    val_speakers.append(moved_speaker)
                    val_videos.extend(moved_speaker[1])
                    print(f"      Moved {speaker_id} to validation for word '{missing_word}'")
                    break
    
    return {
        'train': {'speakers': [s[0] for s in train_speakers], 'videos': train_videos},
        'val': {'speakers': [s[0] for s in val_speakers], 'videos': val_videos},
        'test': {'speakers': [s[0] for s in test_speakers], 'videos': test_videos}
    }

def copy_videos_to_corrected_splits(splits):
    """Copy videos to corrected split directories"""
    print(f"\n📁 Creating corrected speaker-separated directories...")
    
    # Create output directory
    output_base = Path("data/speaker_separated_corrected")
    if output_base.exists():
        shutil.rmtree(output_base)
    output_base.mkdir(parents=True)
    
    for split_name in ['train', 'val', 'test']:
        split_dir = output_base / split_name
        split_dir.mkdir(exist_ok=True)
        
        videos = splits[split_name]['videos']
        print(f"   Copying {len(videos)} videos to {split_name}...")
        
        for video in videos:
            src_path = Path(video['path'])
            if src_path.exists():
                # Keep original filename
                dst_path = split_dir / video['filename']
                
                try:
                    shutil.copy2(src_path, dst_path)
                except Exception as e:
                    print(f"      ❌ Failed to copy {src_path}: {e}")
            else:
                print(f"      ⚠️  Source file not found: {src_path}")
    
    print(f"   ✅ Videos copied to: {output_base}")
    return output_base

def create_corrected_manifests(splits, output_base):
    """Create manifest files for corrected splits"""
    print(f"\n📝 Creating corrected manifest files...")
    
    words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
    
    for split_name in ['train', 'val', 'test']:
        videos = splits[split_name]['videos']
        manifest_data = []
        
        for video in videos:
            # Update path to point to new location
            new_path = output_base / split_name / video['filename']
            
            manifest_data.append({
                'video_path': str(new_path),
                'word': video['word'],
                'label': words.index(video['word']),
                'split': split_name,
                'speaker': video['speaker_id'],
                'filename': video['filename'],
                'video_num': video['video_num'],
                'original_split': video.get('original_split', 'unknown')
            })
        
        # Save manifest
        manifest_df = pd.DataFrame(manifest_data)
        manifest_path = output_base / f"{split_name}_manifest.csv"
        manifest_df.to_csv(manifest_path, index=False)
        
        print(f"   ✅ {split_name} manifest: {manifest_path} ({len(manifest_df)} videos)")
    
    # Combined manifest
    all_manifests = []
    for split_name in ['train', 'val', 'test']:
        videos = splits[split_name]['videos']
        for video in videos:
            new_path = output_base / split_name / video['filename']
            
            all_manifests.append({
                'video_path': str(new_path),
                'word': video['word'],
                'label': words.index(video['word']),
                'split': split_name,
                'speaker': video['speaker_id'],
                'filename': video['filename'],
                'video_num': video['video_num'],
                'original_split': video.get('original_split', 'unknown')
            })
    
    combined_df = pd.DataFrame(all_manifests)
    combined_path = output_base / "combined_manifest.csv"
    combined_df.to_csv(combined_path, index=False)
    print(f"   ✅ Combined manifest: {combined_path} ({len(combined_df)} videos)")
    
    return output_base

def verify_corrected_separation(splits):
    """Verify the corrected speaker separation"""
    print(f"\n✅ Verifying corrected speaker separation...")
    
    train_speakers = set(splits['train']['speakers'])
    val_speakers = set(splits['val']['speakers'])
    test_speakers = set(splits['test']['speakers'])
    
    # Check for overlaps
    train_val_overlap = train_speakers & val_speakers
    train_test_overlap = train_speakers & test_speakers
    val_test_overlap = val_speakers & test_speakers
    
    overlaps_found = train_val_overlap or train_test_overlap or val_test_overlap
    
    if overlaps_found:
        print(f"   ❌ Still have overlaps:")
        if train_val_overlap:
            print(f"      Train-Val: {train_val_overlap}")
        if train_test_overlap:
            print(f"      Train-Test: {train_test_overlap}")
        if val_test_overlap:
            print(f"      Val-Test: {val_test_overlap}")
        return False
    else:
        print(f"   ✅ Perfect speaker separation achieved!")
        print(f"      Training: {len(train_speakers)} unique speakers")
        print(f"      Validation: {len(val_speakers)} unique speakers")
        print(f"      Test: {len(test_speakers)} unique speakers")
        return True

def main():
    """Main function to fix speaker separation"""
    print("🔧 Fixing Speaker Separation Issues")
    print("=" * 60)
    
    # Load current analysis
    analysis_data = load_current_analysis()
    if not analysis_data:
        return False
    
    # Create corrected splits
    splits = create_speaker_separated_splits(analysis_data)
    
    # Verify separation
    separation_ok = verify_corrected_separation(splits)
    
    if not separation_ok:
        print("❌ Failed to achieve perfect separation")
        return False
    
    # Copy videos to new directories
    output_base = copy_videos_to_corrected_splits(splits)
    
    # Create manifests
    manifest_dir = create_corrected_manifests(splits, output_base)
    
    # Save correction info
    correction_info = {
        'train_speakers': splits['train']['speakers'],
        'val_speakers': splits['val']['speakers'],
        'test_speakers': splits['test']['speakers'],
        'train_videos': len(splits['train']['videos']),
        'val_videos': len(splits['val']['videos']),
        'test_videos': len(splits['test']['videos']),
        'output_directory': str(output_base),
        'perfect_separation': separation_ok,
        'correction_timestamp': pd.Timestamp.now().isoformat()
    }
    
    info_path = output_base / "correction_info.json"
    with open(info_path, 'w') as f:
        json.dump(correction_info, f, indent=2)
    
    print(f"\n🎉 Speaker separation fixed successfully!")
    print(f"   📁 Corrected dataset: {output_base}")
    print(f"   📊 Training: {len(splits['train']['videos'])} videos, {len(splits['train']['speakers'])} speakers")
    print(f"   📊 Validation: {len(splits['val']['videos'])} videos, {len(splits['val']['speakers'])} speakers")
    print(f"   📊 Test: {len(splits['test']['videos'])} videos, {len(splits['test']['speakers'])} speakers")
    print(f"   ✅ Perfect speaker separation: {separation_ok}")
    
    print(f"\n🚀 Ready for preprocessing and training!")
    
    return True

if __name__ == "__main__":
    success = main()
