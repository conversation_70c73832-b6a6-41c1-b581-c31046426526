#!/usr/bin/env python3
"""
Final ensemble approach with data quality analysis and multiple model fusion.
Last attempt to achieve >80% validation accuracy.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import pandas as pd
import numpy as np
from pathlib import Path
import json
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, classification_report
from sklearn.ensemble import VotingClassifier
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier
import warnings
warnings.filterwarnings('ignore')

class DataQualityAnalyzer:
    """Analyze data quality and identify problematic samples"""
    
    def __init__(self, manifest_path):
        self.manifest = pd.read_csv(manifest_path)
        self.quality_scores = {}
        
    def analyze_video_quality(self, video_path):
        """Analyze individual video quality"""
        try:
            video_tensor = torch.load(video_path)
            
            # Basic quality metrics
            quality_score = 1.0
            
            # Check tensor shape consistency
            if video_tensor.dim() != 4 or video_tensor.size(0) != 1:
                quality_score -= 0.3
            
            # Check for extreme values (poor normalization)
            if torch.abs(video_tensor).max() > 5:
                quality_score -= 0.2
            
            # Check for constant frames (static video)
            frame_variance = torch.var(video_tensor, dim=1).mean()
            if frame_variance < 0.01:
                quality_score -= 0.3
            
            # Check temporal consistency
            temporal_diff = torch.diff(video_tensor, dim=1).abs().mean()
            if temporal_diff < 0.001:  # Too static
                quality_score -= 0.2
            
            return max(0.0, quality_score)
            
        except Exception as e:
            print(f"Error analyzing {video_path}: {e}")
            return 0.0
    
    def analyze_dataset(self):
        """Analyze entire dataset quality"""
        print("🔍 Analyzing dataset quality...")
        
        quality_scores = []
        problematic_samples = []
        
        for idx, row in tqdm(self.manifest.iterrows(), total=len(self.manifest)):
            video_path = row['video_path']
            quality = self.analyze_video_quality(video_path)
            quality_scores.append(quality)
            
            if quality < 0.5:
                problematic_samples.append({
                    'path': video_path,
                    'word': row['word'],
                    'speaker': row['speaker'],
                    'quality': quality
                })
        
        self.quality_scores = quality_scores
        
        print(f"📊 Dataset Quality Analysis:")
        print(f"   Average quality score: {np.mean(quality_scores):.3f}")
        print(f"   Problematic samples: {len(problematic_samples)}")
        print(f"   Quality distribution:")
        print(f"     High (>0.8): {sum(1 for q in quality_scores if q > 0.8)}")
        print(f"     Medium (0.5-0.8): {sum(1 for q in quality_scores if 0.5 <= q <= 0.8)}")
        print(f"     Low (<0.5): {sum(1 for q in quality_scores if q < 0.5)}")
        
        return quality_scores, problematic_samples

class EnsembleDataset(Dataset):
    """Dataset with quality-based sample weighting"""
    
    def __init__(self, manifest_path, quality_scores=None, min_quality=0.3):
        self.manifest = pd.read_csv(manifest_path)
        self.quality_scores = quality_scores or [1.0] * len(self.manifest)
        self.min_quality = min_quality
        
        # Filter out very low quality samples
        valid_indices = [i for i, q in enumerate(self.quality_scores) if q >= min_quality]
        self.manifest = self.manifest.iloc[valid_indices].reset_index(drop=True)
        self.quality_scores = [self.quality_scores[i] for i in valid_indices]
        
        print(f"📊 Filtered dataset: {len(self.manifest)} samples (min quality: {min_quality})")
        
    def __len__(self):
        return len(self.manifest)
    
    def __getitem__(self, idx):
        row = self.manifest.iloc[idx]
        
        # Load preprocessed tensor
        video_tensor = torch.load(row['video_path'])
        
        # Ensure correct shape
        if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
            pass
        elif video_tensor.dim() == 3:
            video_tensor = video_tensor.unsqueeze(0)
        else:
            raise ValueError(f"Unexpected tensor shape: {video_tensor.shape}")
        
        label = torch.tensor(row['label'], dtype=torch.long)
        quality = self.quality_scores[idx]
        
        return video_tensor, label, quality

class SimpleEnsembleModel(nn.Module):
    """Simple ensemble of multiple architectures"""
    
    def __init__(self, num_classes=5):
        super(SimpleEnsembleModel, self).__init__()
        
        # Model 1: Simple CNN
        self.cnn_model = nn.Sequential(
            nn.Conv3d(1, 16, kernel_size=(3, 7, 7), stride=(1, 2, 2), padding=(1, 3, 3)),
            nn.ReLU(),
            nn.MaxPool3d((2, 2, 2)),
            nn.Conv3d(16, 32, kernel_size=(3, 5, 5), stride=(1, 1, 1), padding=(1, 2, 2)),
            nn.ReLU(),
            nn.MaxPool3d((2, 2, 2)),
            nn.AdaptiveAvgPool3d((1, 1, 1)),
            nn.Flatten(),
            nn.Linear(32, 64),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(64, num_classes)
        )
        
        # Model 2: Temporal averaging
        self.temporal_model = nn.Sequential(
            nn.Conv3d(1, 8, kernel_size=(1, 7, 7), stride=(1, 2, 2), padding=(0, 3, 3)),
            nn.ReLU(),
            nn.MaxPool3d((1, 2, 2)),
            nn.Conv3d(8, 16, kernel_size=(1, 5, 5), stride=(1, 1, 1), padding=(0, 2, 2)),
            nn.ReLU(),
            nn.AdaptiveAvgPool3d((1, 1, 1)),
            nn.Flatten(),
            nn.Linear(16, 32),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(32, num_classes)
        )
        
        # Fusion layer
        self.fusion = nn.Sequential(
            nn.Linear(num_classes * 2, 32),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(32, num_classes)
        )
    
    def forward(self, x):
        # Get predictions from both models
        cnn_out = self.cnn_model(x)
        temporal_out = self.temporal_model(x)
        
        # Concatenate and fuse
        combined = torch.cat([cnn_out, temporal_out], dim=1)
        output = self.fusion(combined)
        
        return output

class EnsembleTrainer:
    """Ensemble trainer with quality weighting"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  Using device: {self.device}")
        
        self.batch_size = 8
        self.learning_rate = 0.0005
        self.epochs = 50
        self.target_accuracy = 80.0
        
        # Create output directory
        self.output_dir = Path("artifacts/vsr_fasthead_v1")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🎯 Final Ensemble Approach:")
        print(f"   Target: {self.target_accuracy}% validation accuracy")
        print(f"   Strategy: Quality filtering + Ensemble + Weighted training")
    
    def train_with_quality_weighting(self, train_loader, val_loader):
        """Train ensemble model with quality weighting"""
        
        # Create ensemble model
        model = SimpleEnsembleModel(num_classes=5)
        model.to(self.device)
        
        total_params = sum(p.numel() for p in model.parameters())
        print(f"🤖 Ensemble model parameters: {total_params:,}")
        
        # Optimizer with strong regularization
        optimizer = optim.AdamW(
            model.parameters(),
            lr=self.learning_rate,
            weight_decay=0.05
        )
        
        # Scheduler
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=self.epochs,
            eta_min=1e-6
        )
        
        # Loss function
        criterion = nn.CrossEntropyLoss(reduction='none')  # No reduction for quality weighting
        
        best_val_acc = 0.0
        patience = 15
        epochs_without_improvement = 0
        
        for epoch in range(self.epochs):
            print(f"\n📅 Ensemble Epoch {epoch+1}/{self.epochs}")
            print("-" * 50)
            
            # Training with quality weighting
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for videos, labels, qualities in tqdm(train_loader, desc="Training"):
                videos, labels = videos.to(self.device), labels.to(self.device)
                qualities = qualities.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(videos)
                
                # Quality-weighted loss
                losses = criterion(outputs, labels)
                weighted_loss = (losses * qualities).mean()
                
                weighted_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()
                
                train_loss += weighted_loss.item()
                _, predicted = outputs.max(1)
                train_total += labels.size(0)
                train_correct += predicted.eq(labels).sum().item()
            
            # Validation
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            all_predictions = []
            all_labels = []
            
            with torch.no_grad():
                for videos, labels, qualities in tqdm(val_loader, desc="Validation"):
                    videos, labels = videos.to(self.device), labels.to(self.device)
                    
                    outputs = model(videos)
                    loss = criterion(outputs, labels).mean()
                    
                    val_loss += loss.item()
                    _, predicted = outputs.max(1)
                    val_total += labels.size(0)
                    val_correct += predicted.eq(labels).sum().item()
                    
                    all_predictions.extend(predicted.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())
            
            scheduler.step()
            
            train_acc = 100. * train_correct / train_total
            val_acc = 100. * val_correct / val_total
            
            print(f"📊 Results:")
            print(f"   Train Loss: {train_loss/len(train_loader):.4f} | Train Acc: {train_acc:.2f}%")
            print(f"   Val Loss: {val_loss/len(val_loader):.4f} | Val Acc: {val_acc:.2f}%")
            
            # Check improvement
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                epochs_without_improvement = 0
                
                # Save best model
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'accuracy': val_acc,
                    'training_config': {
                        'batch_size': self.batch_size,
                        'learning_rate': self.learning_rate,
                        'architecture': 'QualityWeightedEnsemble'
                    }
                }, self.output_dir / 'ensemble_best.pth')
                
                print(f"🎉 NEW BEST: {val_acc:.2f}% validation accuracy!")
                
                # Print classification report
                words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
                report = classification_report(all_labels, all_predictions, target_names=words, digits=3)
                print(f"\n📋 Classification Report:")
                print(report)
                
                # Check if target achieved
                if val_acc >= self.target_accuracy:
                    print(f"\n🎯 TARGET ACHIEVED: {self.target_accuracy:.1f}% validation accuracy!")
                    print(f"   Final validation accuracy: {val_acc:.2f}%")
                    print(f"   🏆 SUCCESS with Quality-Weighted Ensemble!")
                    return True
                
            else:
                epochs_without_improvement += 1
            
            # Early stopping
            if epochs_without_improvement >= patience:
                print(f"\n⏹️  Early stopping: No improvement for {patience} epochs")
                break
        
        print(f"\n✅ Ensemble Training completed!")
        print(f"   Best validation accuracy: {best_val_acc:.2f}%")
        print(f"   Target achieved: {'YES! 🎉' if best_val_acc >= self.target_accuracy else 'Not yet'}")
        
        return best_val_acc >= self.target_accuracy
    
    def train(self):
        """Main training function with quality analysis"""
        print("🚀 Starting Final Ensemble Approach")
        print("=" * 70)
        
        # Step 1: Analyze data quality
        print("🔍 Step 1: Data Quality Analysis")
        train_analyzer = DataQualityAnalyzer("data/speaker_separated_processed/train_processed_manifest.csv")
        train_quality_scores, train_problematic = train_analyzer.analyze_dataset()
        
        val_analyzer = DataQualityAnalyzer("data/speaker_separated_processed/val_processed_manifest.csv")
        val_quality_scores, val_problematic = val_analyzer.analyze_dataset()
        
        # Step 2: Create quality-filtered datasets
        print("\n🔧 Step 2: Creating Quality-Filtered Datasets")
        train_dataset = EnsembleDataset(
            "data/speaker_separated_processed/train_processed_manifest.csv",
            train_quality_scores,
            min_quality=0.3
        )
        
        val_dataset = EnsembleDataset(
            "data/speaker_separated_processed/val_processed_manifest.csv",
            val_quality_scores,
            min_quality=0.0  # Keep all validation samples
        )
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=2,
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=2,
            pin_memory=True
        )
        
        # Step 3: Train ensemble model
        print("\n🎯 Step 3: Training Quality-Weighted Ensemble")
        success = self.train_with_quality_weighting(train_loader, val_loader)
        
        return success

def main():
    """Main function for final ensemble approach"""
    print("🎯 Final Ensemble Approach for >80% Validation Accuracy")
    print("=" * 70)
    
    # Check if processed data exists
    train_manifest = Path("data/speaker_separated_processed/train_processed_manifest.csv")
    val_manifest = Path("data/speaker_separated_processed/val_processed_manifest.csv")
    
    if not train_manifest.exists() or not val_manifest.exists():
        print("❌ Processed dataset not found!")
        return False
    
    # Create ensemble trainer
    trainer = EnsembleTrainer()
    success = trainer.train()
    
    if success:
        print(f"\n🎉 FINAL SUCCESS: >80% validation accuracy achieved!")
        print(f"📁 Model saved: {trainer.output_dir}/ensemble_best.pth")
    else:
        print(f"\n⚠️  Final attempt completed")
        print(f"💡 Dataset appears to be extremely challenging")
        print(f"🔍 Consider:")
        print(f"   • Manual data quality review")
        print(f"   • Additional data collection")
        print(f"   • Different preprocessing approach")
        print(f"   • Lower target accuracy (e.g., 60-70%)")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
