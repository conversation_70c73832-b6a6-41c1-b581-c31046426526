{"train_data": {"dataset_name": "Training Set", "total_videos": 79, "videos_data": [{"filename": "help 1.mp4", "word": "help", "speaker_id": "speaker_01", "video_num": 1, "path": "data/TRAINING SET/help 1.mp4", "properties": {"frame_count": 46, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.5348666666666666}}, {"filename": "doctor 16.mp4", "word": "doctor", "speaker_id": "speaker_16", "video_num": 16, "path": "data/TRAINING SET/doctor 16.mp4", "properties": {"frame_count": 64, "fps": 23.976023976023978, "width": 1280, "height": 720, "duration": 2.6693333333333333}}, {"filename": "glasses 20.mp4", "word": "glasses", "speaker_id": "speaker_20", "video_num": 20, "path": "data/TRAINING SET/glasses 20.mp4", "properties": {"frame_count": 90, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 3.003}}, {"filename": "pillow 13.mp4", "word": "pillow", "speaker_id": "speaker_13", "video_num": 13, "path": "data/TRAINING SET/pillow 13.mp4", "properties": {"frame_count": 61, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.0353666666666665}}, {"filename": "pillow 6.mp4", "word": "pillow", "speaker_id": "speaker_06", "video_num": 6, "path": "data/TRAINING SET/pillow 6.mp4", "properties": {"frame_count": 45, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.5015}}, {"filename": "pillow 7.mp4", "word": "pillow", "speaker_id": "speaker_07", "video_num": 7, "path": "data/TRAINING SET/pillow 7.mp4", "properties": {"frame_count": 67, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.2355666666666667}}, {"filename": "pillow 12.mp4", "word": "pillow", "speaker_id": "speaker_12", "video_num": 12, "path": "data/TRAINING SET/pillow 12.mp4", "properties": {"frame_count": 66, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.2022}}, {"filename": "doctor 17.mp4", "word": "doctor", "speaker_id": "speaker_17", "video_num": 17, "path": "data/TRAINING SET/doctor 17.mp4", "properties": {"frame_count": 61, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.0353666666666665}}, {"filename": "help 2.mp4", "word": "help", "speaker_id": "speaker_02", "video_num": 2, "path": "data/TRAINING SET/help 2.mp4", "properties": {"frame_count": 43, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.4347666666666667}}, {"filename": "doctor 15.mp4", "word": "doctor", "speaker_id": "speaker_15", "video_num": 15, "path": "data/TRAINING SET/doctor 15.mp4", "properties": {"frame_count": 48, "fps": 23.976023976023978, "width": 1280, "height": 720, "duration": 2.002}}, {"filename": "doctor 1.mp4", "word": "doctor", "speaker_id": "speaker_01", "video_num": 1, "path": "data/TRAINING SET/doctor 1.mp4", "properties": {"frame_count": 55, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.8351666666666666}}, {"filename": "pillow 5.mp4", "word": "pillow", "speaker_id": "speaker_05", "video_num": 5, "path": "data/TRAINING SET/pillow 5.mp4", "properties": {"frame_count": 49, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.6349666666666667}}, {"filename": "pillow 4.mp4", "word": "pillow", "speaker_id": "speaker_04", "video_num": 4, "path": "data/TRAINING SET/pillow 4.mp4", "properties": {"frame_count": 55, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.8351666666666666}}, {"filename": "doctor 14.mp4", "word": "doctor", "speaker_id": "speaker_14", "video_num": 14, "path": "data/TRAINING SET/doctor 14.mp4", "properties": {"frame_count": 33, "fps": 23.976023976023978, "width": 1280, "height": 720, "duration": 1.376375}}, {"filename": "help 3.mp4", "word": "help", "speaker_id": "speaker_03", "video_num": 3, "path": "data/TRAINING SET/help 3.mp4", "properties": {"frame_count": 55, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.8351666666666666}}, {"filename": "pillow 15.mp4", "word": "pillow", "speaker_id": "speaker_15", "video_num": 15, "path": "data/TRAINING SET/pillow 15.mp4", "properties": {"frame_count": 36, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.2012}}, {"filename": "doctor 10.mp4", "word": "doctor", "speaker_id": "speaker_10", "video_num": 10, "path": "data/TRAINING SET/doctor 10.mp4", "properties": {"frame_count": 43, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.4347666666666667}}, {"filename": "doctor 4.mp4", "word": "doctor", "speaker_id": "speaker_04", "video_num": 4, "path": "data/TRAINING SET/doctor 4.mp4", "properties": {"frame_count": 44, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.4681333333333333}}, {"filename": "pillow 1.mp4", "word": "pillow", "speaker_id": "speaker_01", "video_num": 1, "path": "data/TRAINING SET/pillow 1.mp4", "properties": {"frame_count": 42, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.4014}}, {"filename": "doctor 5.mp4", "word": "doctor", "speaker_id": "speaker_05", "video_num": 5, "path": "data/TRAINING SET/doctor 5.mp4", "properties": {"frame_count": 74, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.469133333333333}}, {"filename": "pillow 14.mp4", "word": "pillow", "speaker_id": "speaker_14", "video_num": 14, "path": "data/TRAINING SET/pillow 14.mp4", "properties": {"frame_count": 67, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.2355666666666667}}, {"filename": "help 6.mp4", "word": "help", "speaker_id": "speaker_06", "video_num": 6, "path": "data/TRAINING SET/help 6.mp4", "properties": {"frame_count": 51, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.7017}}, {"filename": "help 4.mp4", "word": "help", "speaker_id": "speaker_04", "video_num": 4, "path": "data/TRAINING SET/help 4.mp4", "properties": {"frame_count": 36, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.2012}}, {"filename": "glasses 19.mp4", "word": "glasses", "speaker_id": "speaker_19", "video_num": 19, "path": "data/TRAINING SET/glasses 19.mp4", "properties": {"frame_count": 41, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.3680333333333334}}, {"filename": "doctor 7.mp4", "word": "doctor", "speaker_id": "speaker_07", "video_num": 7, "path": "data/TRAINING SET/doctor 7.mp4", "properties": {"frame_count": 48, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.6016000000000001}}, {"filename": "pillow 3.mp4", "word": "pillow", "speaker_id": "speaker_03", "video_num": 3, "path": "data/TRAINING SET/pillow 3.mp4", "properties": {"frame_count": 48, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.6016000000000001}}, {"filename": "pillow 2.mp4", "word": "pillow", "speaker_id": "speaker_02", "video_num": 2, "path": "data/TRAINING SET/pillow 2.mp4", "properties": {"frame_count": 60, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.0020000000000002}}, {"filename": "doctor 6.mp4", "word": "doctor", "speaker_id": "speaker_06", "video_num": 6, "path": "data/TRAINING SET/doctor 6.mp4", "properties": {"frame_count": 30, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.0010000000000001}}, {"filename": "glasses 18.mp4", "word": "glasses", "speaker_id": "speaker_18", "video_num": 18, "path": "data/TRAINING SET/glasses 18.mp4", "properties": {"frame_count": 86, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.8695333333333335}}, {"filename": "doctor 12.mp4", "word": "doctor", "speaker_id": "speaker_12", "video_num": 12, "path": "data/TRAINING SET/doctor 12.mp4", "properties": {"frame_count": 56, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.8685333333333334}}, {"filename": "pillow 17.mp4", "word": "pillow", "speaker_id": "speaker_17", "video_num": 17, "path": "data/TRAINING SET/pillow 17.mp4", "properties": {"frame_count": 37, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.2345666666666666}}, {"filename": "help 5.mp4", "word": "help", "speaker_id": "speaker_05", "video_num": 5, "path": "data/TRAINING SET/help 5.mp4", "properties": {"frame_count": 29, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 0.9676333333333333}}, {"filename": "help 16.mp4", "word": "help", "speaker_id": "speaker_16", "video_num": 16, "path": "data/TRAINING SET/help 16.mp4", "properties": {"frame_count": 87, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.9029000000000003}}, {"filename": "phone 20.mp4", "word": "phone", "speaker_id": "speaker_20", "video_num": 20, "path": "data/TRAINING SET/phone 20.mp4", "properties": {"frame_count": 57, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.9019}}, {"filename": "help 17.mp4", "word": "help", "speaker_id": "speaker_17", "video_num": 17, "path": "data/TRAINING SET/help 17.mp4", "properties": {"frame_count": 51, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.7017}}, {"filename": "help 14.mp4", "word": "help", "speaker_id": "speaker_14", "video_num": 14, "path": "data/TRAINING SET/help 14.mp4", "properties": {"frame_count": 40, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.3346666666666667}}, {"filename": "help 11.mp4", "word": "help", "speaker_id": "speaker_11", "video_num": 11, "path": "data/TRAINING SET/help 11.mp4", "properties": {"frame_count": 61, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.0353666666666665}}, {"filename": "help 13.mp4", "word": "help", "speaker_id": "speaker_13", "video_num": 13, "path": "data/TRAINING SET/help 13.mp4", "properties": {"frame_count": 53, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.7684333333333333}}, {"filename": "phone 18.mp4", "word": "phone", "speaker_id": "speaker_18", "video_num": 18, "path": "data/TRAINING SET/phone 18.mp4", "properties": {"frame_count": 89, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.9696333333333333}}, {"filename": "phone 19.mp4", "word": "phone", "speaker_id": "speaker_19", "video_num": 19, "path": "data/TRAINING SET/phone 19.mp4", "properties": {"frame_count": 35, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.1678333333333333}}, {"filename": "help 12.mp4", "word": "help", "speaker_id": "speaker_12", "video_num": 12, "path": "data/TRAINING SET/help 12.mp4", "properties": {"frame_count": 43, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.4347666666666667}}, {"filename": "phone 14.mp4", "word": "phone", "speaker_id": "speaker_14", "video_num": 14, "path": "data/TRAINING SET/phone 14.mp4", "properties": {"frame_count": 50, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.6683333333333334}}, {"filename": "glasses 1.mp4", "word": "glasses", "speaker_id": "speaker_01", "video_num": 1, "path": "data/TRAINING SET/glasses 1.mp4", "properties": {"frame_count": 48, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.6016000000000001}}, {"filename": "help 20.mp4", "word": "help", "speaker_id": "speaker_20", "video_num": 20, "path": "data/TRAINING SET/help 20.mp4", "properties": {"frame_count": 66, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.2022}}, {"filename": "glasses 3.mp4", "word": "glasses", "speaker_id": "speaker_03", "video_num": 3, "path": "data/TRAINING SET/glasses 3.mp4", "properties": {"frame_count": 41, "fps": 25.0, "width": 1280, "height": 720, "duration": 1.64}}, {"filename": "phone 17.mp4", "word": "phone", "speaker_id": "speaker_17", "video_num": 17, "path": "data/TRAINING SET/phone 17.mp4", "properties": {"frame_count": 62, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.0687333333333333}}, {"filename": "phone 16.mp4", "word": "phone", "speaker_id": "speaker_16", "video_num": 16, "path": "data/TRAINING SET/phone 16.mp4", "properties": {"frame_count": 45, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.5015}}, {"filename": "glasses 2.mp4", "word": "glasses", "speaker_id": "speaker_02", "video_num": 2, "path": "data/TRAINING SET/glasses 2.mp4", "properties": {"frame_count": 81, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.7027}}, {"filename": "help 19.mp4", "word": "help", "speaker_id": "speaker_19", "video_num": 19, "path": "data/TRAINING SET/help 19.mp4", "properties": {"frame_count": 166, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 5.538866666666666}}, {"filename": "phone 12.mp4", "word": "phone", "speaker_id": "speaker_12", "video_num": 12, "path": "data/TRAINING SET/phone 12.mp4", "properties": {"frame_count": 55, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.8351666666666666}}, {"filename": "glasses 6.mp4", "word": "glasses", "speaker_id": "speaker_06", "video_num": 6, "path": "data/TRAINING SET/glasses 6.mp4", "properties": {"frame_count": 54, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.8018}}, {"filename": "phone 13.mp4", "word": "phone", "speaker_id": "speaker_13", "video_num": 13, "path": "data/TRAINING SET/phone 13.mp4", "properties": {"frame_count": 67, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.2355666666666667}}, {"filename": "help 18.mp4", "word": "help", "speaker_id": "speaker_18", "video_num": 18, "path": "data/TRAINING SET/help 18.mp4", "properties": {"frame_count": 71, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.3690333333333333}}, {"filename": "phone 11.mp4", "word": "phone", "speaker_id": "speaker_11", "video_num": 11, "path": "data/TRAINING SET/phone 11.mp4", "properties": {"frame_count": 57, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.9019}}, {"filename": "glasses 5.mp4", "word": "glasses", "speaker_id": "speaker_05", "video_num": 5, "path": "data/TRAINING SET/glasses 5.mp4", "properties": {"frame_count": 60, "fps": 25.0, "width": 1280, "height": 720, "duration": 2.4}}, {"filename": "glasses 4.mp4", "word": "glasses", "speaker_id": "speaker_04", "video_num": 4, "path": "data/TRAINING SET/glasses 4.mp4", "properties": {"frame_count": 38, "fps": 25.0, "width": 1280, "height": 720, "duration": 1.52}}, {"filename": "phone 10.mp4", "word": "phone", "speaker_id": "speaker_10", "video_num": 10, "path": "data/TRAINING SET/phone 10.mp4", "properties": {"frame_count": 44, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.4681333333333333}}, {"filename": "glasses 15.mp4", "word": "glasses", "speaker_id": "speaker_15", "video_num": 15, "path": "data/TRAINING SET/glasses 15.mp4", "properties": {"frame_count": 56, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.8685333333333334}}, {"filename": "phone 1.mp4", "word": "phone", "speaker_id": "speaker_01", "video_num": 1, "path": "data/TRAINING SET/phone 1.mp4", "properties": {"frame_count": 36, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.2012}}, {"filename": "glasses 14.mp4", "word": "glasses", "speaker_id": "speaker_14", "video_num": 14, "path": "data/TRAINING SET/glasses 14.mp4", "properties": {"frame_count": 64, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.135466666666667}}, {"filename": "pillow 19.mp4", "word": "pillow", "speaker_id": "speaker_19", "video_num": 19, "path": "data/TRAINING SET/pillow 19.mp4", "properties": {"frame_count": 65, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.168833333333333}}, {"filename": "glasses 16.mp4", "word": "glasses", "speaker_id": "speaker_16", "video_num": 16, "path": "data/TRAINING SET/glasses 16.mp4", "properties": {"frame_count": 53, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.7684333333333333}}, {"filename": "phone 2.mp4", "word": "phone", "speaker_id": "speaker_02", "video_num": 2, "path": "data/TRAINING SET/phone 2.mp4", "properties": {"frame_count": 53, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.7684333333333333}}, {"filename": "doctor 8.mp4", "word": "doctor", "speaker_id": "speaker_08", "video_num": 8, "path": "data/TRAINING SET/doctor 8.mp4", "properties": {"frame_count": 44, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.4681333333333333}}, {"filename": "doctor 9.mp4", "word": "doctor", "speaker_id": "speaker_09", "video_num": 9, "path": "data/TRAINING SET/doctor 9.mp4", "properties": {"frame_count": 47, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.5682333333333334}}, {"filename": "phone 3.mp4", "word": "phone", "speaker_id": "speaker_03", "video_num": 3, "path": "data/TRAINING SET/phone 3.mp4", "properties": {"frame_count": 51, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.7017}}, {"filename": "pillow 18.mp4", "word": "pillow", "speaker_id": "speaker_18", "video_num": 18, "path": "data/TRAINING SET/pillow 18.mp4", "properties": {"frame_count": 62, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.0687333333333333}}, {"filename": "doctor 19.mp4", "word": "doctor", "speaker_id": "speaker_19", "video_num": 19, "path": "data/TRAINING SET/doctor 19.mp4", "properties": {"frame_count": 35, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.1678333333333333}}, {"filename": "pillow 20.mp4", "word": "pillow", "speaker_id": "speaker_20", "video_num": 20, "path": "data/TRAINING SET/pillow 20.mp4", "properties": {"frame_count": 59, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.9686333333333335}}, {"filename": "glasses 13.mp4", "word": "glasses", "speaker_id": "speaker_13", "video_num": 13, "path": "data/TRAINING SET/glasses 13.mp4", "properties": {"frame_count": 62, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.0687333333333333}}, {"filename": "phone 7.mp4", "word": "phone", "speaker_id": "speaker_07", "video_num": 7, "path": "data/TRAINING SET/phone 7.mp4", "properties": {"frame_count": 52, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.7350666666666668}}, {"filename": "pillow 9.mp4", "word": "pillow", "speaker_id": "speaker_09", "video_num": 9, "path": "data/TRAINING SET/pillow 9.mp4", "properties": {"frame_count": 45, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.5015}}, {"filename": "pillow 8.mp4", "word": "pillow", "speaker_id": "speaker_08", "video_num": 8, "path": "data/TRAINING SET/pillow 8.mp4", "properties": {"frame_count": 63, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.1021}}, {"filename": "phone 6.mp4", "word": "phone", "speaker_id": "speaker_06", "video_num": 6, "path": "data/TRAINING SET/phone 6.mp4", "properties": {"frame_count": 46, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.5348666666666666}}, {"filename": "glasses 12.mp4", "word": "glasses", "speaker_id": "speaker_12", "video_num": 12, "path": "data/TRAINING SET/glasses 12.mp4", "properties": {"frame_count": 77, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.5692333333333335}}, {"filename": "doctor 18.mp4", "word": "doctor", "speaker_id": "speaker_18", "video_num": 18, "path": "data/TRAINING SET/doctor 18.mp4", "properties": {"frame_count": 44, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.4681333333333333}}, {"filename": "phone 4.mp4", "word": "phone", "speaker_id": "speaker_04", "video_num": 4, "path": "data/TRAINING SET/phone 4.mp4", "properties": {"frame_count": 40, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.3346666666666667}}, {"filename": "phone 5.mp4", "word": "phone", "speaker_id": "speaker_05", "video_num": 5, "path": "data/TRAINING SET/phone 5.mp4", "properties": {"frame_count": 42, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.4014}}, {"filename": "glasses 11.mp4", "word": "glasses", "speaker_id": "speaker_11", "video_num": 11, "path": "data/TRAINING SET/glasses 11.mp4", "properties": {"frame_count": 63, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.1021}}], "word_counts": {"help": 15, "doctor": 15, "glasses": 15, "pillow": 17, "phone": 17}, "speaker_counts": {"speaker_01": 5, "speaker_16": 4, "speaker_20": 4, "speaker_13": 4, "speaker_06": 5, "speaker_07": 3, "speaker_12": 5, "speaker_17": 4, "speaker_02": 4, "speaker_15": 3, "speaker_05": 5, "speaker_04": 5, "speaker_14": 5, "speaker_03": 4, "speaker_10": 2, "speaker_19": 5, "speaker_18": 5, "speaker_11": 3, "speaker_08": 2, "speaker_09": 2}, "speaker_words": {"speaker_01": ["doctor", "pillow", "glasses", "help", "phone"], "speaker_16": ["glasses", "help", "phone", "doctor"], "speaker_20": ["pillow", "help", "phone", "glasses"], "speaker_13": ["pillow", "glasses", "phone", "help"], "speaker_06": ["doctor", "pillow", "glasses", "help", "phone"], "speaker_07": ["pillow", "phone", "doctor"], "speaker_12": ["doctor", "pillow", "help", "glasses", "phone"], "speaker_17": ["pillow", "help", "phone", "doctor"], "speaker_02": ["pillow", "glasses", "phone", "help"], "speaker_15": ["pillow", "glasses", "doctor"], "speaker_05": ["doctor", "pillow", "help", "glasses", "phone"], "speaker_04": ["doctor", "pillow", "help", "glasses", "phone"], "speaker_14": ["doctor", "pillow", "help", "glasses", "phone"], "speaker_03": ["pillow", "glasses", "phone", "help"], "speaker_10": ["phone", "doctor"], "speaker_19": ["doctor", "pillow", "help", "glasses", "phone"], "speaker_18": ["doctor", "pillow", "help", "glasses", "phone"], "speaker_11": ["glasses", "phone", "help"], "speaker_08": ["pillow", "doctor"], "speaker_09": ["pillow", "doctor"]}, "missing_words": [], "all_speakers": ["speaker_01", "speaker_16", "speaker_20", "speaker_13", "speaker_06", "speaker_07", "speaker_12", "speaker_17", "speaker_02", "speaker_15", "speaker_05", "speaker_04", "speaker_14", "speaker_03", "speaker_10", "speaker_19", "speaker_18", "speaker_11", "speaker_08", "speaker_09"]}, "val_data": {"dataset_name": "Validation Set", "total_videos": 11, "videos_data": [{"filename": "phone 8.mp4", "word": "phone", "speaker_id": "speaker_08", "video_num": 8, "path": "data/VAL SET/phone 8.mp4", "properties": {"frame_count": 43, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.4347666666666667}}, {"filename": "doctor 2.mp4", "word": "doctor", "speaker_id": "speaker_02", "video_num": 2, "path": "data/VAL SET/doctor 2.mp4", "properties": {"frame_count": 57, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.9019}}, {"filename": "pillow 10.mp4", "word": "pillow", "speaker_id": "speaker_10", "video_num": 10, "path": "data/VAL SET/pillow 10.mp4", "properties": {"frame_count": 48, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.6016000000000001}}, {"filename": "help 7.mp4", "word": "help", "speaker_id": "speaker_07", "video_num": 7, "path": "data/VAL SET/help 7.mp4", "properties": {"frame_count": 41, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.3680333333333334}}, {"filename": "doctor 11.mp4", "word": "doctor", "speaker_id": "speaker_11", "video_num": 11, "path": "data/VAL SET/doctor 11.mp4", "properties": {"frame_count": 39, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.3013000000000001}}, {"filename": "pillow 16.mp4", "word": "pillow", "speaker_id": "speaker_16", "video_num": 16, "path": "data/VAL SET/pillow 16.mp4", "properties": {"frame_count": 55, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.8351666666666666}}, {"filename": "glasses 9.mp4", "word": "glasses", "speaker_id": "speaker_09", "video_num": 9, "path": "data/VAL SET/glasses 9.mp4", "properties": {"frame_count": 37, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.2345666666666666}}, {"filename": "phone 15.mp4", "word": "phone", "speaker_id": "speaker_15", "video_num": 15, "path": "data/VAL SET/phone 15.mp4", "properties": {"frame_count": 57, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.9019}}, {"filename": "glasses 7.mp4", "word": "glasses", "speaker_id": "speaker_07", "video_num": 7, "path": "data/VAL SET/glasses 7.mp4", "properties": {"frame_count": 44, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.4681333333333333}}, {"filename": "help 9.mp4", "word": "help", "speaker_id": "speaker_09", "video_num": 9, "path": "data/VAL SET/help 9.mp4", "properties": {"frame_count": 40, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.3346666666666667}}, {"filename": "glasses 17.mp4", "word": "glasses", "speaker_id": "speaker_17", "video_num": 17, "path": "data/VAL SET/glasses 17.mp4", "properties": {"frame_count": 50, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.6683333333333334}}], "word_counts": {"phone": 2, "doctor": 2, "pillow": 2, "help": 2, "glasses": 3}, "speaker_counts": {"speaker_08": 1, "speaker_02": 1, "speaker_10": 1, "speaker_07": 2, "speaker_11": 1, "speaker_16": 1, "speaker_09": 2, "speaker_15": 1, "speaker_17": 1}, "speaker_words": {"speaker_08": ["phone"], "speaker_02": ["doctor"], "speaker_10": ["pillow"], "speaker_07": ["glasses", "help"], "speaker_11": ["doctor"], "speaker_16": ["pillow"], "speaker_09": ["help", "glasses"], "speaker_15": ["phone"], "speaker_17": ["glasses"]}, "missing_words": [], "all_speakers": ["speaker_08", "speaker_02", "speaker_10", "speaker_07", "speaker_11", "speaker_16", "speaker_09", "speaker_15", "speaker_17"]}, "test_data": {"dataset_name": "Test Set", "total_videos": 10, "videos_data": [{"filename": "doctor 3.mp4", "word": "doctor", "speaker_id": "speaker_03", "video_num": 3, "path": "data/TEST SET/doctor 3.mp4", "properties": {"frame_count": 62, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 2.0687333333333333}}, {"filename": "phone 9.mp4", "word": "phone", "speaker_id": "speaker_09", "video_num": 9, "path": "data/TEST SET/phone 9.mp4", "properties": {"frame_count": 46, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.5348666666666666}}, {"filename": "pillow 11.mp4", "word": "pillow", "speaker_id": "speaker_11", "video_num": 11, "path": "data/TEST SET/pillow 11.mp4", "properties": {"frame_count": 53, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.7684333333333333}}, {"filename": "doctor 13.mp4", "word": "doctor", "speaker_id": "speaker_13", "video_num": 13, "path": "data/TEST SET/doctor 13.mp4", "properties": {"frame_count": 34, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.1344666666666667}}, {"filename": "glasses 8.mp4", "word": "glasses", "speaker_id": "speaker_08", "video_num": 8, "path": "data/TEST SET/glasses 8.mp4", "properties": {"frame_count": 43, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.4347666666666667}}, {"filename": "help 15.mp4", "word": "help", "speaker_id": "speaker_15", "video_num": 15, "path": "data/TEST SET/help 15.mp4", "properties": {"frame_count": 44, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.4681333333333333}}, {"filename": "help 10.mp4", "word": "help", "speaker_id": "speaker_10", "video_num": 10, "path": "data/TEST SET/help 10.mp4", "properties": {"frame_count": 38, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.2679333333333334}}, {"filename": "help 8.mp4", "word": "help", "speaker_id": "speaker_08", "video_num": 8, "path": "data/TEST SET/help 8.mp4", "properties": {"frame_count": 37, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.2345666666666666}}, {"filename": "doctor 20.mp4", "word": "doctor", "speaker_id": "speaker_20", "video_num": 20, "path": "data/TEST SET/doctor 20.mp4", "properties": {"frame_count": 46, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.5348666666666666}}, {"filename": "glasses 10.mp4", "word": "glasses", "speaker_id": "speaker_10", "video_num": 10, "path": "data/TEST SET/glasses 10.mp4", "properties": {"frame_count": 35, "fps": 29.97002997002997, "width": 1280, "height": 720, "duration": 1.1678333333333333}}], "word_counts": {"doctor": 3, "phone": 1, "pillow": 1, "glasses": 2, "help": 3}, "speaker_counts": {"speaker_03": 1, "speaker_09": 1, "speaker_11": 1, "speaker_13": 1, "speaker_08": 2, "speaker_15": 1, "speaker_10": 2, "speaker_20": 1}, "speaker_words": {"speaker_03": ["doctor"], "speaker_09": ["phone"], "speaker_11": ["pillow"], "speaker_13": ["doctor"], "speaker_08": ["help", "glasses"], "speaker_15": ["help"], "speaker_10": ["glasses", "help"], "speaker_20": ["doctor"]}, "missing_words": [], "all_speakers": ["speaker_03", "speaker_09", "speaker_11", "speaker_13", "speaker_08", "speaker_15", "speaker_10", "speaker_20"]}, "speaker_separation_perfect": false, "total_videos": 100, "total_speakers": 20, "manifest_directory": "data/current_dataset_manifests"}