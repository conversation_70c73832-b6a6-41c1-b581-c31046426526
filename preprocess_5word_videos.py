#!/usr/bin/env python3
"""
Preprocess 5-word dataset videos for LipNet training.
Convert 1280x720 MP4 videos to 112x112 grayscale, 25 FPS, 16 frames.
"""

import os
import cv2
import numpy as np
import pandas as pd
import torch
from pathlib import Path
import json
from tqdm import tqdm
import mediapipe as mp

class VideoPreprocessor:
    """Preprocess videos for LipNet training"""
    
    def __init__(self, target_size=(112, 112), target_fps=25, num_frames=64):
        self.target_size = target_size
        self.target_fps = target_fps
        self.num_frames = num_frames
        
        # Initialize MediaPipe Face Detection for lip region extraction
        self.mp_face_detection = mp.solutions.face_detection
        self.mp_drawing = mp.solutions.drawing_utils
        self.face_detection = self.mp_face_detection.FaceDetection(
            model_selection=0, min_detection_confidence=0.5)
    
    def extract_lip_region(self, frame):
        """Extract lip region from frame using MediaPipe"""
        try:
            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.face_detection.process(rgb_frame)
            
            if results.detections:
                detection = results.detections[0]  # Use first detection
                bbox = detection.location_data.relative_bounding_box
                
                h, w = frame.shape[:2]
                
                # Convert relative coordinates to absolute
                x = int(bbox.xmin * w)
                y = int(bbox.ymin * h)
                width = int(bbox.width * w)
                height = int(bbox.height * h)
                
                # Focus on lower half of face (mouth region)
                mouth_y = y + int(height * 0.6)  # Start from 60% down the face
                mouth_height = int(height * 0.4)  # Take bottom 40% of face
                
                # Add some padding
                padding = int(min(width, mouth_height) * 0.1)
                mouth_x = max(0, x - padding)
                mouth_y = max(0, mouth_y - padding)
                mouth_width = min(w - mouth_x, width + 2 * padding)
                mouth_height = min(h - mouth_y, mouth_height + 2 * padding)
                
                # Extract mouth region
                mouth_region = frame[mouth_y:mouth_y + mouth_height, 
                                  mouth_x:mouth_x + mouth_width]
                
                if mouth_region.size > 0:
                    return mouth_region
            
            # Fallback: use center crop if face detection fails
            h, w = frame.shape[:2]
            center_y, center_x = h // 2, w // 2
            crop_size = min(h, w) // 3  # Take center third
            
            y1 = max(0, center_y - crop_size // 2)
            y2 = min(h, center_y + crop_size // 2)
            x1 = max(0, center_x - crop_size // 2)
            x2 = min(w, center_x + crop_size // 2)
            
            return frame[y1:y2, x1:x2]
            
        except Exception as e:
            print(f"Error in lip extraction: {e}")
            # Return center crop as fallback
            h, w = frame.shape[:2]
            center_y, center_x = h // 2, w // 2
            crop_size = min(h, w) // 3
            
            y1 = max(0, center_y - crop_size // 2)
            y2 = min(h, center_y + crop_size // 2)
            x1 = max(0, center_x - crop_size // 2)
            x2 = min(w, center_x + crop_size // 2)
            
            return frame[y1:y2, x1:x2]
    
    def process_video(self, video_path, output_path):
        """Process a single video file"""
        try:
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                print(f"❌ Cannot open video: {video_path}")
                return False
            
            # Get video properties
            original_fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # Calculate frame sampling
            if frame_count <= self.num_frames:
                # If video has fewer frames, repeat last frame
                frame_indices = list(range(frame_count))
                while len(frame_indices) < self.num_frames:
                    frame_indices.append(frame_count - 1)
            else:
                # Sample frames evenly
                frame_indices = np.linspace(0, frame_count - 1, self.num_frames, dtype=int)
            
            frames = []
            
            for frame_idx in frame_indices:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                
                if not ret:
                    print(f"⚠️  Cannot read frame {frame_idx} from {video_path}")
                    # Use last valid frame if available
                    if frames:
                        frame = frames[-1].copy()
                    else:
                        continue
                
                # Extract lip region
                lip_region = self.extract_lip_region(frame)
                
                # Convert to grayscale
                if len(lip_region.shape) == 3:
                    lip_region = cv2.cvtColor(lip_region, cv2.COLOR_BGR2GRAY)
                
                # Resize to target size
                lip_region = cv2.resize(lip_region, self.target_size, interpolation=cv2.INTER_AREA)
                
                # Normalize to [0, 1]
                lip_region = lip_region.astype(np.float32) / 255.0
                
                frames.append(lip_region)
            
            cap.release()
            
            if len(frames) != self.num_frames:
                print(f"⚠️  Expected {self.num_frames} frames, got {len(frames)} for {video_path}")
                return False
            
            # Stack frames into tensor: (num_frames, height, width)
            video_tensor = np.stack(frames, axis=0)
            
            # Apply z-score normalization
            mean = np.mean(video_tensor)
            std = np.std(video_tensor)
            if std > 0:
                video_tensor = (video_tensor - mean) / std
            
            # Convert to torch tensor and add channel dimension: (1, num_frames, height, width)
            video_tensor = torch.from_numpy(video_tensor).unsqueeze(0)
            
            # Save as .pt file
            output_path.parent.mkdir(parents=True, exist_ok=True)
            torch.save(video_tensor, output_path)
            
            return True
            
        except Exception as e:
            print(f"❌ Error processing {video_path}: {e}")
            return False

def main():
    """Main preprocessing function"""
    
    print("🎬 Preprocessing 5-word dataset for LipNet training...")
    print("=" * 60)
    
    # Load manifests
    manifest_dir = Path("data/5word_dataset")
    train_df = pd.read_csv(manifest_dir / "train_manifest.csv")
    val_df = pd.read_csv(manifest_dir / "val_manifest.csv")
    
    # Create output directory
    output_dir = Path("data/5word_processed")
    output_dir.mkdir(exist_ok=True)
    
    # Initialize preprocessor
    preprocessor = VideoPreprocessor(target_size=(112, 112), target_fps=25, num_frames=64)
    
    # Process training videos
    print(f"\n📹 Processing {len(train_df)} training videos...")
    train_success = 0
    train_processed = []
    
    for idx, row in tqdm(train_df.iterrows(), total=len(train_df), desc="Training videos"):
        video_path = Path(row['video_path'])
        output_name = f"{row['word']}_{video_path.stem}_processed.pt"
        output_path = output_dir / "train" / output_name
        
        if preprocessor.process_video(video_path, output_path):
            train_success += 1
            train_processed.append({
                'video_path': str(output_path),
                'original_path': str(video_path),
                'word': row['word'],
                'label': row['label'],
                'split': 'train',
                'speaker': row['speaker']
            })
    
    print(f"✅ Training: {train_success}/{len(train_df)} videos processed successfully")
    
    # Process validation videos
    print(f"\n📹 Processing {len(val_df)} validation videos...")
    val_success = 0
    val_processed = []
    
    for idx, row in tqdm(val_df.iterrows(), total=len(val_df), desc="Validation videos"):
        video_path = Path(row['video_path'])
        output_name = f"{row['word']}_{video_path.stem}_processed.pt"
        output_path = output_dir / "val" / output_name
        
        if preprocessor.process_video(video_path, output_path):
            val_success += 1
            val_processed.append({
                'video_path': str(output_path),
                'original_path': str(video_path),
                'word': row['word'],
                'label': row['label'],
                'split': 'val',
                'speaker': row['speaker']
            })
    
    print(f"✅ Validation: {val_success}/{len(val_df)} videos processed successfully")
    
    # Save processed manifests
    if train_processed:
        train_processed_df = pd.DataFrame(train_processed)
        train_processed_df.to_csv(output_dir / "train_processed_manifest.csv", index=False)
        print(f"📝 Saved train_processed_manifest.csv ({len(train_processed)} videos)")
    
    if val_processed:
        val_processed_df = pd.DataFrame(val_processed)
        val_processed_df.to_csv(output_dir / "val_processed_manifest.csv", index=False)
        print(f"📝 Saved val_processed_manifest.csv ({len(val_processed)} videos)")
    
    # Combined processed manifest
    if train_processed and val_processed:
        combined_processed = train_processed + val_processed
        combined_df = pd.DataFrame(combined_processed)
        combined_df.to_csv(output_dir / "combined_processed_manifest.csv", index=False)
        print(f"📝 Saved combined_processed_manifest.csv ({len(combined_processed)} videos)")
    
    # Save processing summary
    processing_summary = {
        'total_videos': len(train_df) + len(val_df),
        'successfully_processed': train_success + val_success,
        'training_videos': {
            'total': len(train_df),
            'processed': train_success,
            'success_rate': train_success / len(train_df) if len(train_df) > 0 else 0
        },
        'validation_videos': {
            'total': len(val_df),
            'processed': val_success,
            'success_rate': val_success / len(val_df) if len(val_df) > 0 else 0
        },
        'preprocessing_config': {
            'target_size': [112, 112],
            'target_fps': 25,
            'num_frames': 64,
            'normalization': 'z-score',
            'color_space': 'grayscale'
        }
    }
    
    with open(output_dir / "processing_summary.json", 'w') as f:
        json.dump(processing_summary, f, indent=2)
    
    print(f"\n📊 Processing Summary:")
    print(f"   Total videos: {processing_summary['total_videos']}")
    print(f"   Successfully processed: {processing_summary['successfully_processed']}")
    print(f"   Overall success rate: {processing_summary['successfully_processed'] / processing_summary['total_videos']:.1%}")
    
    print(f"\n✅ Video preprocessing complete!")
    print(f"📁 Processed videos saved to: {output_dir}")
    
    # Cleanup MediaPipe
    preprocessor.face_detection.close()

if __name__ == "__main__":
    main()
