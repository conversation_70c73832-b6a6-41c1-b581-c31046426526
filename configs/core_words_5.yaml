# LipNet 5-Word ICU Classification Configuration
# Adapted for doctor, glasses, help, phone, pillow classification

# Core vocabulary (5 anchor words for clinical communication)
phrases:
  - "doctor"
  - "glasses"
  - "help"
  - "phone"
  - "pillow"

# Video processing parameters - Grayscale for LipNet
video:
  num_frames: 64          # LipNet expects 64 frames
  frame_size: [112, 112]  # LipNet input size
  grayscale: true         # Grayscale for LipNet
  use_mouth_detection: true
  mouth_detection_fallback: true

# Transforms and normalization - Z-score normalization for LipNet
transforms:
  normalize:
    mean: [0.0]  # Z-score normalization applied during preprocessing
    std: [1.0]

# LipNet Model parameters - Classification head for 5 words
model:
  architecture: "lipnet"
  input_channels: 1       # Grayscale input
  num_classes: 5
  hidden_dim: 256
  num_rnn_layers: 2
  rnn_type: "LSTM"        # BiLSTM for temporal modeling
  dropout: 0.3
  freeze_encoder: true    # Start with frozen 3D CNN encoder
  use_attention: true     # Multi-head attention mechanism

# Training parameters - Quick training workflow for LipNet
training:
  batch_size: 32
  learning_rate: 0.001   # Higher LR for head-only training
  weight_decay: 0.01
  epochs: 8              # Quick training: 8 epochs as specified
  gradient_clip_norm: 1.0
  early_stopping_patience: 5
  min_delta: 0.01
  target_accuracy: 0.80  # Stop when >80% validation accuracy achieved

# Fine-tuning parameters - Unfreeze encoder if needed
finetune:
  unfreeze_at_epoch: 4   # After 4 epochs, unfreeze encoder if accuracy < 80%
  lr_encoder: 0.0001     # Lower LR for encoder fine-tuning
  lr_head: 0.0005        # Moderate LR for head

# Optimizer
optimizer:
  type: "adamw"
  lr: 0.001
  weight_decay: 0.01
  betas: [0.9, 0.999]

# Learning rate scheduler
scheduler:
  type: "cosine_annealing_warm_restarts"
  T_0: 8
  T_mult: 1
  eta_min: 0.0001

# Loss function
loss:
  type: "cross_entropy"
  label_smoothing: 0.1

# Dataset configuration - Use processed 5-word dataset
dataset:
  use_predefined_splits: true
  respect_manifest_split: true
  train_manifest: "data/5word_processed/train_processed_manifest.csv"
  val_manifest: "data/5word_processed/val_processed_manifest.csv"
  speaker_column: "speaker"
  num_workers: 2
  pin_memory: true

# Data augmentation - Aggressive augmentation for small dataset
augmentation:
  brightness_contrast_range: 0.2
  scale_range: 0.15
  vertical_jitter: 8
  temporal_jitter: 4
  rotation_range: 10
  horizontal_flip: 0.5
  gaussian_noise: 0.02
  temporal_dropout: 0.1

# Mixed precision training
amp: true

# Evaluation metrics
metrics:
  primary: "accuracy"
  track: ["accuracy", "precision", "recall", "f1_macro", "f1_per_class", "confusion_matrix"]

# Clinical validation requirements
clinical:
  target_accuracy: 0.80   # Primary goal: >80% validation accuracy
  min_per_class_accuracy: 0.70
  confidence_threshold: 0.80  # ICU confidence threshold

# Output paths - Save to vsr_fasthead_v1 as specified
paths:
  output_dir: "artifacts/vsr_fasthead_v1"
  checkpoint_dir: "artifacts/vsr_fasthead_v1/checkpoints"
  logs_dir: "artifacts/vsr_fasthead_v1/logs"
  best_model_path: "artifacts/vsr_fasthead_v1/lipnet_5word_best.pth"
