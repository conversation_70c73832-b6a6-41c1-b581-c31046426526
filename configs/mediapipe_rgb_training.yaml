# MediaPipe RGB Training Configuration
# Updated for proper MediaPipe-processed lip ROI data
# RGB 112x112 input with ImageNet normalization

# Core vocabulary (5 anchor words for clinical communication)
phrases:
  - "pillow"
  - "help"
  - "glasses"
  - "phone"
  - "doctor"

# Data configuration for MediaPipe-processed videos
data:
  # MediaPipe preprocessing outputs
  preprocessed_data_dir: "data/processed_final_full"
  manifest_path: "data/processed_final_full/manifest_clean.csv"
  
  # Video parameters (matching MediaPipe output)
  frames: 32                    # MediaPipe outputs 32 frames
  height: 112                   # MediaPipe outputs 112x112
  width: 112
  channels: 3                   # RGB format
  grayscale: false             # RGB input
  
  # Data splits (pre-split in manifest with speaker-wise separation)
  use_manifest_splits: true    # Use splits from manifest_clean.csv
  train_ratio: 0.70            # For compatibility (actual splits in manifest)
  val_ratio: 0.20
  test_ratio: 0.10
  split_by_speaker: false      # Already done in manifest
  stratify_by_phrase: false    # Already balanced in manifest
  random_seed: 42
  
  # Augmentation (reduced since MediaPipe crops are already optimized)
  augmentation:
    enabled: true
    # Spatial augmentations (light since crops are already optimized)
    horizontal_flip: 0.0       # Don't flip lips
    random_crop_scale: [0.95, 1.0]  # Very light cropping
    rotation_degrees: 2        # Minimal rotation
    
    # Photometric augmentations
    brightness_factor: 0.1     # Light brightness changes
    contrast_factor: 0.1       # Light contrast changes
    saturation_factor: 0.05    # Very light saturation (RGB)
    hue_factor: 0.02          # Minimal hue shift
    
    # Temporal augmentations
    temporal_crop: 0.1         # Light temporal jittering
    frame_dropout: 0.05        # Occasional frame dropout
    
    # Noise and blur
    gaussian_noise_std: 0.01   # Light noise
    gaussian_blur_prob: 0.1    # Occasional blur
    gaussian_blur_sigma: [0.5, 1.0]

# Transforms and normalization - ImageNet stats for pretrained models
transforms:
  normalize:
    mean: [0.485, 0.456, 0.406]  # ImageNet RGB normalization
    std: [0.229, 0.224, 0.225]
  
  # Additional transforms
  resize: [112, 112]             # Ensure consistent size
  to_tensor: true
  
# Model configuration (torchvision R3D-18 based)
model:
  name: "R3D18_Classifier"
  backbone: "torchvision.models.video.r3d_18"
  pretrained: true              # Use ImageNet pretrained weights
  num_classes: 5                # 5 core words
  
  # Architecture details
  spatial_size: 112             # Input spatial size
  temporal_size: 32             # Input temporal size
  input_channels: 3             # RGB input
  
  # Transfer learning strategy
  freeze_backbone: true         # Start with frozen backbone
  freeze_epochs: 3              # Unfreeze after 3 epochs (faster warmup)
  
  # Classifier head
  dropout: 0.3                  # Dropout in classifier
  hidden_dim: 512               # Hidden dimension before final layer

# Training configuration (optimized for MediaPipe data)
training:
  batch_size: 16                # Increased batch size for stable training
  num_epochs: 20                # Reduced epochs for high-quality data
  
  # Learning rates
  learning_rate: 0.001          # Higher LR for head training
  backbone_lr: 0.0001           # Lower LR for backbone fine-tuning
  
  # Optimizer
  optimizer: "AdamW"
  weight_decay: 0.01
  betas: [0.9, 0.999]
  
  # Scheduler
  scheduler: "CosineAnnealingLR"
  warmup_epochs: 3              # Short warmup
  min_lr: 0.00001
  
  # Early stopping
  early_stopping:
    enabled: true
    patience: 10                # Reasonable patience
    min_delta: 0.005            # Minimum improvement
    monitor: "val_accuracy"     # Monitor validation accuracy
  
  # Gradient clipping
  grad_clip_norm: 1.0
  
  # Validation and checkpointing
  val_every_n_epochs: 1         # Validate every epoch
  save_every_n_epochs: 5        # Save checkpoints every 5 epochs
  save_best_model: true

# Regularization
regularization:
  label_smoothing: 0.1          # Light label smoothing
  mixup_alpha: 0.2              # Mixup augmentation
  cutmix_alpha: 0.0             # No cutmix (can hurt lip reading)
  
  # Class balancing
  class_weights: "balanced"     # Auto-balance classes
  focal_loss: false             # Use standard CE loss first

# Hardware configuration
hardware:
  num_workers: 0                # Single-threaded to avoid pickling issues
  pin_memory: true              # GPU memory optimization
  mixed_precision: true         # AMP for faster training
  
  # Device settings
  device: "auto"                # Auto-detect GPU/CPU
  deterministic: true           # Reproducible results

# Checkpoints and outputs
checkpoints:
  save_dir: "artifacts/mediapipe_rgb_training"
  experiment_name: "mediapipe_rgb_5words"
  
  # What to save
  save_model_state: true
  save_optimizer_state: true
  save_scheduler_state: true
  save_config: true
  
  # Model export
  export_torchscript: true      # Export for deployment
  export_onnx: false            # Skip ONNX for now

# Logging and monitoring
logging:
  log_level: "INFO"
  log_every_n_steps: 10         # Log training progress
  
  # Metrics to track
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
    - "confusion_matrix"
  
  # Validation metrics
  val_metrics:
    - "accuracy"
    - "macro_f1"
    - "per_class_f1"
    - "confusion_matrix"

# Success criteria (based on MediaPipe quality)
success_criteria:
  target_val_accuracy: 0.90     # Target >90% validation accuracy
  target_macro_f1: 0.85         # Target >85% macro F1
  convergence_epochs: 15        # Should converge within 15 epochs
  
  # Quality gates
  min_train_accuracy: 0.95      # Training should reach 95%+
  max_overfitting_gap: 0.10     # Train-val gap should be <10%

# Experiment tracking
experiment:
  name: "mediapipe_rgb_lipreading"
  description: "Training on MediaPipe-processed RGB lip crops with proper ROI extraction"
  tags: ["mediapipe", "rgb", "lip_roi", "5_words", "clinical"]
  
  # Baseline comparison
  baseline_model: null          # No baseline yet
  expected_improvement: "Significant accuracy improvement due to proper lip ROI extraction"

# Debug and validation
debug:
  save_sample_batches: true     # Save sample batches for inspection
  validate_data_loading: true   # Validate data loading pipeline
  check_normalization: true     # Check normalization statistics
  
  # Sanity checks
  overfit_single_batch: false   # Don't overfit single batch by default
  fast_dev_run: false           # Don't run fast dev run by default
