#!/usr/bin/env python3
"""
Complete pipeline upgrade with R(2+1)D-18 transfer learning for >80% validation accuracy.
Part 1: Expand validation set with additional videos from 'top 5 dataset 30.8.25'
Part 2: Replace architecture with proven torchvision R(2+1)D-18 + BiGRU approach
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.models.video as video_models
import torchvision.transforms as transforms
import pandas as pd
import numpy as np
from pathlib import Path
import json
import cv2
import mediapipe as mp
from tqdm import tqdm
import time
from sklearn.metrics import accuracy_score, classification_report
import warnings
warnings.filterwarnings('ignore')

class ValidationSetExpander:
    """Expand validation set with additional videos from top 5 dataset"""
    
    def __init__(self):
        self.mp_face_detection = mp.solutions.face_detection.FaceDetection(
            model_selection=0, min_detection_confidence=0.5
        )
        self.mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        
        # Mouth landmark indices for MediaPipe
        self.MOUTH_LANDMARKS = [
            61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
            78, 191, 80, 81, 82, 13, 312, 311, 310, 415, 95, 88, 178,
            87, 14, 317, 402, 318, 324, 308
        ]
        
        print("🔧 ValidationSetExpander initialized with MediaPipe")
    
    def extract_mouth_roi(self, frame):
        """Extract mouth ROI using MediaPipe with temporal smoothing"""
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Try FaceMesh first (more accurate for mouth)
        results = self.mp_face_mesh.process(rgb_frame)
        
        if results.multi_face_landmarks:
            landmarks = results.multi_face_landmarks[0]
            h, w = frame.shape[:2]
            
            # Get mouth landmarks
            mouth_points = []
            for idx in self.MOUTH_LANDMARKS:
                x = int(landmarks.landmark[idx].x * w)
                y = int(landmarks.landmark[idx].y * h)
                mouth_points.append([x, y])
            
            mouth_points = np.array(mouth_points)
            
            # Calculate bounding box with padding
            x_min, y_min = mouth_points.min(axis=0)
            x_max, y_max = mouth_points.max(axis=0)
            
            # Add padding and make square
            padding = 20
            x_min = max(0, x_min - padding)
            y_min = max(0, y_min - padding)
            x_max = min(w, x_max + padding)
            y_max = min(h, y_max + padding)
            
            # Make square crop
            center_x = (x_min + x_max) // 2
            center_y = (y_min + y_max) // 2
            size = max(x_max - x_min, y_max - y_min)
            half_size = size // 2
            
            x_min = max(0, center_x - half_size)
            y_min = max(0, center_y - half_size)
            x_max = min(w, center_x + half_size)
            y_max = min(h, center_y + half_size)
            
            return frame[y_min:y_max, x_min:x_max]
        
        # Fallback to face detection
        results = self.mp_face_detection.process(rgb_frame)
        
        if results.detections:
            detection = results.detections[0]
            bbox = detection.location_data.relative_bounding_box
            h, w = frame.shape[:2]
            
            x = int(bbox.xmin * w)
            y = int(bbox.ymin * h + bbox.height * h * 0.6)  # Focus on lower face
            width = int(bbox.width * w)
            height = int(bbox.height * h * 0.4)  # Only lower 40% of face
            
            x = max(0, x)
            y = max(0, y)
            width = min(width, w - x)
            height = min(height, h - y)
            
            return frame[y:y+height, x:x+width]
        
        return None
    
    def preprocess_video(self, video_path, target_frames=64):
        """Preprocess video with MediaPipe mouth extraction"""
        try:
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                return None
            
            frames = []
            frame_count = 0
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # Sample frames evenly
            frame_indices = np.linspace(0, total_frames - 1, target_frames, dtype=int)
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_count in frame_indices:
                    # Extract mouth ROI
                    mouth_roi = self.extract_mouth_roi(frame)
                    
                    if mouth_roi is not None:
                        # Resize to 112x112 and convert to grayscale
                        mouth_roi = cv2.resize(mouth_roi, (112, 112))
                        mouth_roi = cv2.cvtColor(mouth_roi, cv2.COLOR_BGR2GRAY)
                        
                        # Apply CLAHE for contrast enhancement
                        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                        mouth_roi = clahe.apply(mouth_roi)
                        
                        # Normalize to [-1, 1]
                        mouth_roi = mouth_roi.astype(np.float32) / 127.5 - 1.0
                        
                        frames.append(mouth_roi)
                
                frame_count += 1
            
            cap.release()
            
            if len(frames) < target_frames // 2:  # Need at least half the frames
                return None
            
            # Pad or truncate to exact target_frames
            if len(frames) < target_frames:
                # Repeat last frame
                while len(frames) < target_frames:
                    frames.append(frames[-1])
            elif len(frames) > target_frames:
                frames = frames[:target_frames]
            
            # Convert to tensor: (1, T, H, W)
            video_tensor = torch.tensor(np.array(frames), dtype=torch.float32)
            video_tensor = video_tensor.unsqueeze(0)  # Add channel dimension
            
            return video_tensor
            
        except Exception as e:
            print(f"Error processing {video_path}: {e}")
            return None
    
    def expand_validation_set(self, num_additional_per_class=20):
        """Expand validation set with additional videos"""
        print("🚀 Expanding validation set with additional videos")
        print("=" * 70)
        
        # Get list of additional videos
        top5_dir = Path("data/top 5 dataset 30.8.25")
        if not top5_dir.exists():
            print("❌ Top 5 dataset directory not found!")
            return False
        
        # Group videos by word
        word_videos = {
            'doctor': [],
            'glasses': [],
            'help': [],
            'phone': [],
            'pillow': []
        }
        
        for video_file in top5_dir.glob("*.webm"):
            filename = video_file.name
            for word in word_videos.keys():
                if filename.startswith(word + "__"):
                    word_videos[word].append(video_file)
                    break
        
        print("📊 Available additional videos:")
        for word, videos in word_videos.items():
            print(f"   {word}: {len(videos)} videos")
        
        # Create expanded validation directory
        expanded_val_dir = Path("data/expanded_validation_processed")
        expanded_val_dir.mkdir(exist_ok=True)
        
        # Process additional videos for each class
        expanded_manifest = []
        label_map = {'doctor': 0, 'glasses': 1, 'help': 2, 'phone': 3, 'pillow': 4}
        
        for word, videos in word_videos.items():
            print(f"\n🔄 Processing additional {word} videos...")
            
            # Select diverse videos (different speakers/demographics)
            selected_videos = videos[:num_additional_per_class]  # Take first N
            
            for i, video_path in enumerate(tqdm(selected_videos, desc=f"Processing {word}")):
                # Preprocess video
                video_tensor = self.preprocess_video(video_path)
                
                if video_tensor is not None:
                    # Save processed tensor
                    output_filename = f"{word}_additional_{i:03d}_processed.pt"
                    output_path = expanded_val_dir / output_filename
                    torch.save(video_tensor, output_path)

                    # Add to manifest
                    expanded_manifest.append({
                        'video_path': str(output_path),
                        'original_path': str(video_path),
                        'word': word,
                        'label': label_map[word],
                        'split': 'val_expanded',
                        'speaker': f"additional_speaker_{i}",
                        'filename': video_path.name,
                        'processed_filename': output_filename,
                        'extraction_rate': 1.0
                    })
                else:
                    print(f"⚠️  Failed to process {video_path.name}")
        
        # Load existing validation manifest
        existing_manifest = pd.read_csv("data/speaker_separated_processed/val_processed_manifest.csv")
        
        # Combine manifests
        expanded_df = pd.DataFrame(expanded_manifest)
        combined_manifest = pd.concat([existing_manifest, expanded_df], ignore_index=True)
        
        # Save expanded manifest
        expanded_manifest_path = "data/expanded_validation_manifest.csv"
        combined_manifest.to_csv(expanded_manifest_path, index=False)
        
        print(f"\n✅ Validation set expanded!")
        print(f"   Original validation videos: {len(existing_manifest)}")
        print(f"   Additional videos processed: {len(expanded_manifest)}")
        print(f"   Total validation videos: {len(combined_manifest)}")
        print(f"   📁 Expanded manifest: {expanded_manifest_path}")
        
        return True

class R2Plus1DDataset(Dataset):
    """Dataset for R(2+1)D model with 16-frame clips"""
    
    def __init__(self, manifest_path, target_frames=16):
        self.manifest = pd.read_csv(manifest_path)
        self.target_frames = target_frames
        
        # Verify all files exist
        valid_rows = []
        for idx, row in self.manifest.iterrows():
            if Path(row['video_path']).exists():
                valid_rows.append(row)
        
        self.manifest = pd.DataFrame(valid_rows).reset_index(drop=True)
        
        print(f"📊 R(2+1)D Dataset loaded: {len(self.manifest)} videos")
        class_counts = self.manifest['word'].value_counts()
        print(f"   Class distribution: {dict(class_counts)}")
        
        # Kinetics-400 normalization stats
        self.normalize = transforms.Normalize(
            mean=[0.43216, 0.394666, 0.37645],
            std=[0.22803, 0.22145, 0.216989]
        )
    
    def __len__(self):
        return len(self.manifest)
    
    def __getitem__(self, idx):
        row = self.manifest.iloc[idx]
        
        # Load preprocessed tensor
        video_tensor = torch.load(row['video_path'])  # (1, T, H, W)
        
        # Ensure correct shape
        if video_tensor.dim() == 4 and video_tensor.size(0) == 1:
            video_tensor = video_tensor.squeeze(0)  # (T, H, W)
        elif video_tensor.dim() == 3:
            pass  # Already (T, H, W)
        else:
            raise ValueError(f"Unexpected tensor shape: {video_tensor.shape}")
        
        # Sample exactly 16 frames
        T, H, W = video_tensor.shape
        if T >= self.target_frames:
            # Sample evenly
            indices = torch.linspace(0, T - 1, self.target_frames).long()
            video_tensor = video_tensor[indices]
        else:
            # Repeat frames to reach target
            repeat_factor = (self.target_frames + T - 1) // T
            video_tensor = video_tensor.repeat(repeat_factor, 1, 1)[:self.target_frames]
        
        # Convert grayscale to RGB: (T, H, W) -> (T, 3, H, W)
        video_tensor = video_tensor.unsqueeze(1).repeat(1, 3, 1, 1)
        
        # Normalize with Kinetics-400 stats
        for t in range(self.target_frames):
            video_tensor[t] = self.normalize(video_tensor[t])
        
        # Rearrange to (3, T, H, W) for R(2+1)D
        video_tensor = video_tensor.permute(1, 0, 2, 3)
        
        label = torch.tensor(row['label'], dtype=torch.long)
        
        return video_tensor, label

class R2Plus1DLipNet(nn.Module):
    """R(2+1)D-18 + BiGRU classifier for lipreading"""
    
    def __init__(self, num_classes=5, hidden_size=256):
        super(R2Plus1DLipNet, self).__init__()
        
        # Load pretrained R(2+1)D-18
        try:
            # Try new API first
            self.backbone = video_models.r2plus1d_18(weights='KINETICS400_V1')
        except TypeError:
            # Fallback to older API
            self.backbone = video_models.r2plus1d_18(pretrained=True)
        
        # Remove final classifier to get 512-dim features
        self.backbone.fc = nn.Identity()
        
        # BiGRU for temporal modeling
        self.bigru = nn.GRU(
            input_size=512,
            hidden_size=hidden_size,
            num_layers=1,
            batch_first=True,
            bidirectional=True
        )
        
        # Final classifier
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(hidden_size * 2, num_classes)  # *2 for bidirectional
        )
        
        print(f"🤖 R(2+1)D-18 + BiGRU model created")
        print(f"   Backbone: Kinetics-400 pretrained R(2+1)D-18")
        print(f"   BiGRU hidden size: {hidden_size}")
        print(f"   Output classes: {num_classes}")
    
    def forward(self, x):
        # x: (batch, 3, 16, 112, 112)
        batch_size = x.size(0)
        
        # Extract features with R(2+1)D backbone
        features = self.backbone(x)  # (batch, 512)
        
        # For BiGRU, we need sequence dimension
        # Since R(2+1)D processes the entire clip, we'll use the feature as a single timestep
        features = features.unsqueeze(1)  # (batch, 1, 512)
        
        # BiGRU processing
        gru_out, _ = self.bigru(features)  # (batch, 1, 512)
        
        # Take the output from the single timestep
        gru_out = gru_out.squeeze(1)  # (batch, 512)
        
        # Classification
        output = self.classifier(gru_out)
        
        return output

class R2Plus1DTrainer:
    """Two-phase trainer: frozen backbone -> light fine-tuning"""
    
    def __init__(self, use_expanded_validation=True):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  Using device: {self.device}")
        
        self.batch_size = 8
        self.target_accuracy = 80.0
        self.use_expanded_validation = use_expanded_validation
        
        # Create output directory
        self.output_dir = Path("artifacts/vsr_fasthead_v1")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🎯 R(2+1)D Transfer Learning Pipeline:")
        print(f"   Target accuracy: {self.target_accuracy}%")
        print(f"   Batch size: {self.batch_size}")
        print(f"   Two-phase training: Frozen → Fine-tuning")
    
    def create_data_loaders(self):
        """Create data loaders for training and validation"""
        
        # Training dataset (original)
        train_dataset = R2Plus1DDataset(
            "data/speaker_separated_processed/train_processed_manifest.csv",
            target_frames=16
        )
        
        # Validation dataset (expanded or original)
        if self.use_expanded_validation:
            val_manifest_path = "data/expanded_validation_manifest.csv"
            if not Path(val_manifest_path).exists():
                print("⚠️  Expanded validation manifest not found, using original")
                val_manifest_path = "data/speaker_separated_processed/val_processed_manifest.csv"
        else:
            val_manifest_path = "data/speaker_separated_processed/val_processed_manifest.csv"
        
        val_dataset = R2Plus1DDataset(val_manifest_path, target_frames=16)
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=2,
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=2,
            pin_memory=True
        )
        
        return train_loader, val_loader
    
    def phase1_frozen_training(self, model, train_loader, val_loader):
        """Phase 1: Train only BiGRU + classifier with frozen R(2+1)D backbone"""
        print("\n🔒 Phase 1: Frozen Backbone Training")
        print("=" * 50)
        
        # Freeze all backbone parameters
        for param in model.backbone.parameters():
            param.requires_grad = False
        
        # Only train BiGRU and classifier
        trainable_params = list(model.bigru.parameters()) + list(model.classifier.parameters())
        
        optimizer = optim.Adam(trainable_params, lr=1e-3, weight_decay=0.01)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='max', factor=0.5, patience=5, verbose=True
        )
        criterion = nn.CrossEntropyLoss()
        
        best_val_acc = 0.0
        epochs = 20
        patience = 10
        epochs_without_improvement = 0
        
        for epoch in range(epochs):
            print(f"\n📅 Phase 1 Epoch {epoch+1}/{epochs}")
            
            # Training
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            for videos, labels in tqdm(train_loader, desc="Training"):
                videos, labels = videos.to(self.device), labels.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(videos)
                loss = criterion(outputs, labels)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = outputs.max(1)
                train_total += labels.size(0)
                train_correct += predicted.eq(labels).sum().item()
            
            # Validation
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            all_predictions = []
            all_labels = []
            
            with torch.no_grad():
                for videos, labels in tqdm(val_loader, desc="Validation"):
                    videos, labels = videos.to(self.device), labels.to(self.device)
                    
                    outputs = model(videos)
                    loss = criterion(outputs, labels)
                    
                    val_loss += loss.item()
                    _, predicted = outputs.max(1)
                    val_total += labels.size(0)
                    val_correct += predicted.eq(labels).sum().item()
                    
                    all_predictions.extend(predicted.cpu().numpy())
                    all_labels.extend(labels.cpu().numpy())
            
            train_acc = 100. * train_correct / train_total
            val_acc = 100. * val_correct / val_total
            
            print(f"📊 Results:")
            print(f"   Train Loss: {train_loss/len(train_loader):.4f} | Train Acc: {train_acc:.2f}%")
            print(f"   Val Loss: {val_loss/len(val_loader):.4f} | Val Acc: {val_acc:.2f}%")
            
            scheduler.step(val_acc)
            
            # Check improvement
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                epochs_without_improvement = 0
                
                # Save best model
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'accuracy': val_acc,
                    'phase': 1,
                    'training_config': {
                        'batch_size': self.batch_size,
                        'learning_rate': 1e-3,
                        'architecture': 'R2Plus1D_BiGRU'
                    }
                }, self.output_dir / 'r2plus1d_phase1_best.pth')
                
                print(f"🎉 NEW BEST: {val_acc:.2f}% validation accuracy!")
                
                # Print classification report
                words = ['doctor', 'glasses', 'help', 'phone', 'pillow']
                report = classification_report(all_labels, all_predictions, target_names=words, digits=3)
                print(f"\n📋 Classification Report:")
                print(report)
                
                # Check if target achieved
                if val_acc >= self.target_accuracy:
                    print(f"\n🎯 TARGET ACHIEVED: {self.target_accuracy:.1f}% validation accuracy!")
                    return True, best_val_acc
                
            else:
                epochs_without_improvement += 1
            
            # Early stopping
            if epochs_without_improvement >= patience:
                print(f"\n⏹️  Early stopping: No improvement for {patience} epochs")
                break
        
        print(f"\n✅ Phase 1 completed! Best validation accuracy: {best_val_acc:.2f}%")
        return best_val_acc >= self.target_accuracy, best_val_acc

def main():
    """Main function for complete pipeline upgrade"""
    print("🚀 Complete R(2+1)D Pipeline Upgrade for >80% Validation Accuracy")
    print("=" * 80)
    
    # Part 1: Expand validation set
    print("📈 Part 1: Expanding Validation Set")
    expander = ValidationSetExpander()
    expansion_success = expander.expand_validation_set(num_additional_per_class=20)
    
    if not expansion_success:
        print("⚠️  Validation set expansion failed, continuing with original set")
    
    # Part 2: R(2+1)D transfer learning
    print("\n🎯 Part 2: R(2+1)D Transfer Learning Training")
    
    # Create trainer
    trainer = R2Plus1DTrainer(use_expanded_validation=expansion_success)
    
    # Create data loaders
    train_loader, val_loader = trainer.create_data_loaders()
    
    # Create model
    model = R2Plus1DLipNet(num_classes=5, hidden_size=256)
    model.to(trainer.device)
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"📊 Model Statistics:")
    print(f"   Total parameters: {total_params:,}")
    print(f"   Trainable parameters: {trainable_params:,}")
    
    # Phase 1: Frozen backbone training
    phase1_success, phase1_acc = trainer.phase1_frozen_training(model, train_loader, val_loader)
    
    if phase1_success:
        print(f"\n🎉 SUCCESS: Phase 1 achieved {phase1_acc:.2f}% > {trainer.target_accuracy}%!")
        print(f"📁 Model saved: {trainer.output_dir}/r2plus1d_phase1_best.pth")
        return True
    else:
        print(f"\n📊 Phase 1 result: {phase1_acc:.2f}% (target: {trainer.target_accuracy}%)")
        if phase1_acc >= 60.0:
            print("🔄 Phase 1 shows promise, would proceed to Phase 2 fine-tuning")
        else:
            print("⚠️  Phase 1 accuracy too low for fine-tuning")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
